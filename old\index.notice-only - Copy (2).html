<!DOCTYPE html>

<html lang="en-GB">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1, user-scalable=0" name="viewport"/>
<meta content="IE=edge" http-equiv="X-UA-Compatible"/>
<title>NMC UK — Maintenance</title>
<meta content="The Nursing &amp; Midwifery Council - Maintenance" name="title">
<meta content="Nursing and Midwifery Council (NMC) UK maintenance notice." name="description">
<link href="/" rel="canonical"/>
<link href="https://www.nmc.org.uk/static/css/fonts.net.css" rel="stylesheet"/>
<style>
            body {
                margin: 0;
                font-family: sans-serif;
                background: #fff;
                color: #0f172a;
                /* min-height: 100vh;*/
                display: center; 
                align-items: center;
                justify-content: center;

            }
          .notice {
                max-width: 600px;
                margin: 0 auto;
                padding: 2.5rem 2.5rem 2rem 2.5rem;
                background: #fffbe6;
                border-radius: 3rem;
                border: 4px solid #ffd600;
                box-shadow: 0 8px 40px 0 rgba(0,0,0,0.18), 0 2px 12px 0 rgba(0,0,0,0.12);
                text-align: center;
                font-size: 1.35rem;
                font-weight: 600;
                letter-spacing: 0.01em;
                position: relative;
            }
            .notice h2, .notice p {
                text-align: center;
                margin: 0 0 1rem 0;
            }
            .notice ul {
                padding-left: 1.5rem;
                text-align: left;
                display: inline-block;
            }
            .notice li {
                margin-bottom: 0.5rem;
            }
            .notice li:last-child {
                margin-bottom: 0;
            }
            .notice hr {
                margin: 1.5rem 0;
                border: none;
                border-top: 1px solid #d1d3d4;
            }
    border-bottom: 1px dotted
}

b,
strong {
    font-weight: 700
}

dfn {
    font-style: italic
}

h1 {
    margin: .67em 0;
    font-size: 2em
}

mark {
    color: #000;
    background: #ff0
}

small {
    font-size: 80%
}

sub,
sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline
}

sup {
    top: -.5em
}

sub {
    bottom: -.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    height: 0;
    -moz-box-sizing: content-box;
    box-sizing: content-box
}

pre {
    overflow: auto
}

code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font: inherit;
    color: inherit
}

button {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],
html input[disabled] {
    cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    padding: 0;
    border: 0
}

input {
    line-height: normal
}

input[type=checkbox],
input[type=radio] {
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    height: auto
}

input[type=search] {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    -webkit-appearance: textfield
}

input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

fieldset {
    padding: .35em .625em .75em;
    margin: 0 2px;
    border: 1px solid silver
}

legend {
    padding: 0;
    border: 0
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: 700
}

table {
    border-spacing: 0;
    border-collapse: collapse
}

td,
th {
    padding: 0
}

@media print {
    * {
        color: #000!important;
        text-shadow: none!important;
        background: transparent!important;
        box-shadow: none!important
    }
    a,
    a:visited {
        text-decoration: underline
    }
    a[href]:after {
        content: " (" attr(href) ")"
    }
    abbr[title]:after {
        content: " (" attr(title) ")"
    }
    a[href^="javascript:"]:after,
    a[href^="#"]:after {
        content: ""
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    tr,
    img {
        page-break-inside: avoid
    }
    img {
        max-width: 100%!important
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    select {
        background: #fff!important
    }
    .navbar {
        display: none
    }
    .table td,
    .table th {
        background-color: #fff!important
    }
    .btn>.caret,
    .dropup>.btn>.caret {
        border-top-color: #000!important
    }
    .label {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse!important
    }
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #ddd!important
    }
}

@font-face {
    font-family: 'Glyphicons Halflings';
    src: url(../fonts/glyphicons-halflings-regular.eot);
    src: url(../fonts/glyphicons-halflings-regular.eot?#iefix) format('embedded-opentype'), url(../fonts/glyphicons-halflings-regular.woff) format('woff'), url(../fonts/glyphicons-halflings-regular.ttf) format('truetype'), url(../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular) format('svg')
}

.glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.glyphicon-asterisk:before {
    content: "\2a"
}

.glyphicon-plus:before {
    content: "\2b"
}

.glyphicon-euro:before {
    content: "\20ac"
}

.glyphicon-minus:before {
    content: "\2212"
}

.glyphicon-cloud:before {
    content: "\2601"
}

.glyphicon-envelope:before {
    content: "\2709"
}

.glyphicon-pencil:before {
    content: "\270f"
}

.glyphicon-glass:before {
    content: "\e001"
}

.glyphicon-music:before {
    content: "\e002"
}

.glyphicon-search:before {
    content: "\e003"
}

.glyphicon-heart:before {
    content: "\e005"
}

.glyphicon-star:before {
    content: "\e006"
}

.glyphicon-star-empty:before {
    content: "\e007"
}

.glyphicon-user:before {
    content: "\e008"
}

.glyphicon-film:before {
    content: "\e009"
}

.glyphicon-th-large:before {
    content: "\e010"
}

.glyphicon-th:before {
    content: "\e011"
}

.glyphicon-th-list:before {
    content: "\e012"
}

.glyphicon-ok:before {
    content: "\e013"
}

.glyphicon-remove:before {
    content: "\e014"
}

.glyphicon-zoom-in:before {
    content: "\e015"
}

.glyphicon-zoom-out:before {
    content: "\e016"
}

.glyphicon-off:before {
    content: "\e017"
}

.glyphicon-signal:before {
    content: "\e018"
}

.glyphicon-cog:before {
    content: "\e019"
}

.glyphicon-trash:before {
    content: "\e020"
}

.glyphicon-home:before {
    content: "\e021"
}

.glyphicon-file:before {
    content: "\e022"
}

.glyphicon-time:before {
    content: "\e023"
}

.glyphicon-road:before {
    content: "\e024"
}

.glyphicon-download-alt:before {
    content: "\e025"
}

.glyphicon-download:before {
    content: "\e026"
}

.glyphicon-upload:before {
    content: "\e027"
}

.glyphicon-inbox:before {
    content: "\e028"
}

.glyphicon-play-circle:before {
    content: "\e029"
}

.glyphicon-repeat:before {
    content: "\e030"
}

.glyphicon-refresh:before {
    content: "\e031"
}

.glyphicon-list-alt:before {
    content: "\e032"
}

.glyphicon-lock:before {
    content: "\e033"
}

.glyphicon-flag:before {
    content: "\e034"
}

.glyphicon-headphones:before {
    content: "\e035"
}

.glyphicon-volume-off:before {
    content: "\e036"
}

.glyphicon-volume-down:before {
    content: "\e037"
}

.glyphicon-volume-up:before {
    content: "\e038"
}

.glyphicon-qrcode:before {
    content: "\e039"
}

.glyphicon-barcode:before {
    content: "\e040"
}

.glyphicon-tag:before {
    content: "\e041"
}

.glyphicon-tags:before {
    content: "\e042"
}

.glyphicon-book:before {
    content: "\e043"
}

.glyphicon-bookmark:before {
    content: "\e044"
}

.glyphicon-print:before {
    content: "\e045"
}

.glyphicon-camera:before {
    content: "\e046"
}

.glyphicon-font:before {
    content: "\e047"
}

.glyphicon-bold:before {
    content: "\e048"
}

.glyphicon-italic:before {
    content: "\e049"
}

.glyphicon-text-height:before {
    content: "\e050"
}

.glyphicon-text-width:before {
    content: "\e051"
}

.glyphicon-align-left:before {
    content: "\e052"
}

.glyphicon-align-center:before {
    content: "\e053"
}

.glyphicon-align-right:before {
    content: "\e054"
}

.glyphicon-align-justify:before {
    content: "\e055"
}

.glyphicon-list:before {
    content: "\e056"
}

.glyphicon-indent-left:before {
    content: "\e057"
}

.glyphicon-indent-right:before {
    content: "\e058"
}

.glyphicon-facetime-video:before {
    content: "\e059"
}

.glyphicon-picture:before {
    content: "\e060"
}

.glyphicon-map-marker:before {
    content: "\e062"
}

.glyphicon-adjust:before {
    content: "\e063"
}

.glyphicon-tint:before {
    content: "\e064"
}

.glyphicon-edit:before {
    content: "\e065"
}

.glyphicon-share:before {
    content: "\e066"
}

.glyphicon-check:before {
    content: "\e067"
}

.glyphicon-move:before {
    content: "\e068"
}

.glyphicon-step-backward:before {
    content: "\e069"
}

.glyphicon-fast-backward:before {
    content: "\e070"
}

.glyphicon-backward:before {
    content: "\e071"
}

.glyphicon-play:before {
    content: "\e072"
}

.glyphicon-pause:before {
    content: "\e073"
}

.glyphicon-stop:before {
    content: "\e074"
}

.glyphicon-forward:before {
    content: "\e075"
}

.glyphicon-fast-forward:before {
    content: "\e076"
}

.glyphicon-step-forward:before {
    content: "\e077"
}

.glyphicon-eject:before {
    content: "\e078"
}

.glyphicon-chevron-left:before {
    content: "\e079"
}

.glyphicon-chevron-right:before {
    content: "\e080"
}

.glyphicon-plus-sign:before {
    content: "\e081"
}

.glyphicon-minus-sign:before {
    content: "\e082"
}

.glyphicon-remove-sign:before {
    content: "\e083"
}

.glyphicon-ok-sign:before {
    content: "\e084"
}

.glyphicon-question-sign:before {
    content: "\e085"
}

.glyphicon-info-sign:before {
    content: "\e086"
}

.glyphicon-screenshot:before {
    content: "\e087"
}

.glyphicon-remove-circle:before {
    content: "\e088"
}

.glyphicon-ok-circle:before {
    content: "\e089"
}

.glyphicon-ban-circle:before {
    content: "\e090"
}

.glyphicon-arrow-left:before {
    content: "\e091"
}

.glyphicon-arrow-right:before {
    content: "\e092"
}

.glyphicon-arrow-up:before {
    content: "\e093"
}

.glyphicon-arrow-down:before {
    content: "\e094"
}

.glyphicon-share-alt:before {
    content: "\e095"
}

.glyphicon-resize-full:before {
    content: "\e096"
}

.glyphicon-resize-small:before {
    content: "\e097"
}

.glyphicon-exclamation-sign:before {
    content: "\e101"
}

.glyphicon-gift:before {
    content: "\e102"
}

.glyphicon-leaf:before {
    content: "\e103"
}

.glyphicon-fire:before {
    content: "\e104"
}

.glyphicon-eye-open:before {
    content: "\e105"
}

.glyphicon-eye-close:before {
    content: "\e106"
}

.glyphicon-warning-sign:before {
    content: "\e107"
}

.glyphicon-plane:before {
    content: "\e108"
}

.glyphicon-calendar:before {
    content: "\e109"
}

.glyphicon-random:before {
    content: "\e110"
}

.glyphicon-comment:before {
    content: "\e111"
}

.glyphicon-magnet:before {
    content: "\e112"
}

.glyphicon-chevron-up:before {
    content: "\e113"
}

.glyphicon-chevron-down:before {
    content: "\e114"
}

.glyphicon-retweet:before {
    content: "\e115"
}

.glyphicon-shopping-cart:before {
    content: "\e116"
}

.glyphicon-folder-close:before {
    content: "\e117"
}

.glyphicon-folder-open:before {
    content: "\e118"
}

.glyphicon-resize-vertical:before {
    content: "\e119"
}

.glyphicon-resize-horizontal:before {
    content: "\e120"
}

.glyphicon-hdd:before {
    content: "\e121"
}

.glyphicon-bullhorn:before {
    content: "\e122"
}

.glyphicon-bell:before {
    content: "\e123"
}

.glyphicon-certificate:before {
    content: "\e124"
}

.glyphicon-thumbs-up:before {
    content: "\e125"
}

.glyphicon-thumbs-down:before {
    content: "\e126"
}

.glyphicon-hand-right:before {
    content: "\e127"
}

.glyphicon-hand-left:before {
    content: "\e128"
}

.glyphicon-hand-up:before {
    content: "\e129"
}

.glyphicon-hand-down:before {
    content: "\e130"
}

.glyphicon-circle-arrow-right:before {
    content: "\e131"
}

.glyphicon-circle-arrow-left:before {
    content: "\e132"
}

.glyphicon-circle-arrow-up:before {
    content: "\e133"
}

.glyphicon-circle-arrow-down:before {
    content: "\e134"
}

.glyphicon-globe:before {
    content: "\e135"
}

.glyphicon-wrench:before {
    content: "\e136"
}

.glyphicon-tasks:before {
    content: "\e137"
}

.glyphicon-filter:before {
    content: "\e138"
}

.glyphicon-briefcase:before {
    content: "\e139"
}

.glyphicon-fullscreen:before {
    content: "\e140"
}

.glyphicon-dashboard:before {
    content: "\e141"
}

.glyphicon-paperclip:before {
    content: "\e142"
}

.glyphicon-heart-empty:before {
    content: "\e143"
}

.glyphicon-link:before {
    content: "\e144"
}

.glyphicon-phone:before {
    content: "\e145"
}

.glyphicon-pushpin:before {
    content: "\e146"
}

.glyphicon-usd:before {
    content: "\e148"
}

.glyphicon-gbp:before {
    content: "\e149"
}

.glyphicon-sort:before {
    content: "\e150"
}

.glyphicon-sort-by-alphabet:before {
    content: "\e151"
}

.glyphicon-sort-by-alphabet-alt:before {
    content: "\e152"
}

.glyphicon-sort-by-order:before {
    content: "\e153"
}

.glyphicon-sort-by-order-alt:before {
    content: "\e154"
}

.glyphicon-sort-by-attributes:before {
    content: "\e155"
}

.glyphicon-sort-by-attributes-alt:before {
    content: "\e156"
}

.glyphicon-unchecked:before {
    content: "\e157"
}

.glyphicon-expand:before {
    content: "\e158"
}

.glyphicon-collapse-down:before {
    content: "\e159"
}

.glyphicon-collapse-up:before {
    content: "\e160"
}

.glyphicon-log-in:before {
    content: "\e161"
}

.glyphicon-flash:before {
    content: "\e162"
}

.glyphicon-log-out:before {
    content: "\e163"
}

.glyphicon-new-window:before {
    content: "\e164"
}

.glyphicon-record:before {
    content: "\e165"
}

.glyphicon-save:before {
    content: "\e166"
}

.glyphicon-open:before {
    content: "\e167"
}

.glyphicon-saved:before {
    content: "\e168"
}

.glyphicon-import:before {
    content: "\e169"
}

.glyphicon-export:before {
    content: "\e170"
}

.glyphicon-send:before {
    content: "\e171"
}

.glyphicon-floppy-disk:before {
    content: "\e172"
}

.glyphicon-floppy-saved:before {
    content: "\e173"
}

.glyphicon-floppy-remove:before {
    content: "\e174"
}

.glyphicon-floppy-save:before {
    content: "\e175"
}

.glyphicon-floppy-open:before {
    content: "\e176"
}

.glyphicon-credit-card:before {
    content: "\e177"
}

.glyphicon-transfer:before {
    content: "\e178"
}

.glyphicon-cutlery:before {
    content: "\e179"
}

.glyphicon-header:before {
    content: "\e180"
}

.glyphicon-compressed:before {
    content: "\e181"
}

.glyphicon-earphone:before {
    content: "\e182"
}

.glyphicon-phone-alt:before {
    content: "\e183"
}

.glyphicon-tower:before {
    content: "\e184"
}

.glyphicon-stats:before {
    content: "\e185"
}

.glyphicon-sd-video:before {
    content: "\e186"
}

.glyphicon-hd-video:before {
    content: "\e187"
}

.glyphicon-subtitles:before {
    content: "\e188"
}

.glyphicon-sound-stereo:before {
    content: "\e189"
}

.glyphicon-sound-dolby:before {
    content: "\e190"
}

.glyphicon-sound-5-1:before {
    content: "\e191"
}

.glyphicon-sound-6-1:before {
    content: "\e192"
}

.glyphicon-sound-7-1:before {
    content: "\e193"
}

.glyphicon-copyright-mark:before {
    content: "\e194"
}

.glyphicon-registration-mark:before {
    content: "\e195"
}

.glyphicon-cloud-download:before {
    content: "\e197"
}

.glyphicon-cloud-upload:before {
    content: "\e198"
}

.glyphicon-tree-conifer:before {
    content: "\e199"
}

.glyphicon-tree-deciduous:before {
    content: "\e200"
}

* {
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

:before,
:after {
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

html {
    font-size: 10px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

body {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.42857143;
    color: #333;
    background-color: #fff
}

input,
button,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

a {
    color: #428bca;
    text-decoration: none
}

a:hover,
a:focus {
    color: #2a6496;
    text-decoration: underline
}

a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

figure {
    margin: 0
}

img {
    vertical-align: middle
}

.img-responsive,
.thumbnail>img,
.thumbnail a>img,
.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
    display: block;
    width: 100% \9;
    max-width: 100%;
    height: auto
}

.img-rounded {
    border-radius: 6px
}

.img-thumbnail {
    display: inline-block;
    width: 100% \9;
    max-width: 100%;
    height: auto;
    padding: 4px;
    line-height: 1.42857143;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.img-circle {
    border-radius: 50%
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit
}

h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
    font-weight: 400;
    line-height: 1;
    color: #777
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
    margin-top: 20px;
    margin-bottom: 10px
}

h1 small,
.h1 small,
h2 small,
.h2 small,
h3 small,
.h3 small,
h1 .small,
.h1 .small,
h2 .small,
.h2 .small,
h3 .small,
.h3 .small {
    font-size: 65%
}

h4,
.h4,
h5,
.h5,
h6,
.h6 {
    margin-top: 10px;
    margin-bottom: 10px
}

h4 small,
.h4 small,
h5 small,
.h5 small,
h6 small,
.h6 small,
h4 .small,
.h4 .small,
h5 .small,
.h5 .small,
h6 .small,
.h6 .small {
    font-size: 75%
}

h1,
.h1 {
    font-size: 36px
}

h2,
.h2 {
    font-size: 30px
}

h3,
.h3 {
    font-size: 24px
}

h4,
.h4 {
    font-size: 18px
}

h5,
.h5 {
    font-size: 14px
}

h6,
.h6 {
    font-size: 12px
}

p {
    margin: 0 0 10px
}

.lead {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.4
}

@media (min-width:768px) {
    .lead {
        font-size: 21px
    }
}

small,
.small {
    font-size: 85%
}

cite {
    font-style: normal
}

mark,
.mark {
    padding: .2em;
    background-color: #fcf8e3
}

.text-left {
    text-align: left
}

.text-right {
    text-align: right
}

.text-center {
    text-align: center
}

.text-justify {
    text-align: justify
}

.text-nowrap {
    white-space: nowrap
}

.text-lowercase {
    text-transform: lowercase
}

.text-uppercase {
    text-transform: uppercase
}

.text-capitalize {
    text-transform: capitalize
}

.text-muted {
    color: #777
}

.text-primary {
    color: #428bca
}

a.text-primary:hover {
    color: #3071a9
}

.text-success {
    color: #3c763d
}

a.text-success:hover {
    color: #2b542c
}

.text-info {
    color: #31708f
}

a.text-info:hover {
    color: #245269
}

.text-warning {
    color: #8a6d3b
}

a.text-warning:hover {
    color: #66512c
}

.text-danger {
    color: #a94442
}

a.text-danger:hover {
    color: #843534
}

.bg-primary {
    color: #fff;
    background-color: #428bca
}

a.bg-primary:hover {
    background-color: #3071a9
}

.bg-success {
    background-color: #dff0d8
}

a.bg-success:hover {
    background-color: #c1e2b3
}

.bg-info {
    background-color: #d9edf7
}

a.bg-info:hover {
    background-color: #afd9ee
}

.bg-warning {
    background-color: #fcf8e3
}

a.bg-warning:hover {
    background-color: #f7ecb5
}

.bg-danger {
    background-color: #f2dede
}

a.bg-danger:hover {
    background-color: #e4b9b9
}

.page-header {
    padding-bottom: 9px;
    margin: 40px 0 20px;
    border-bottom: 1px solid #eee
}

ul,
ol {
    margin-top: 0;
    margin-bottom: 10px
}

ul ul,
ol ul,
ul ol,
ol ol {
    margin-bottom: 0
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    margin-left: -5px;
    list-style: none
}

.list-inline>li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px
}

dl {
    margin-top: 0;
    margin-bottom: 20px
}

dt,
dd {
    line-height: 1.42857143
}

dt {
    font-weight: 700
}

dd {
    margin-left: 0
}

@media (min-width:768px) {
    .dl-horizontal dt {
        float: left;
        width: 160px;
        overflow: hidden;
        clear: left;
        text-align: right;
        text-overflow: ellipsis;
        white-space: nowrap
    }
    .dl-horizontal dd {
        margin-left: 180px
    }
}

abbr[title],
abbr[data-original-title] {
    cursor: help;
    border-bottom: 1px dotted #777
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee
}

blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
    margin-bottom: 0
}

blockquote footer,
blockquote small,
blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.42857143;
    color: #777
}

blockquote footer:before,
blockquote small:before,
blockquote .small:before {
    content: '\2014 \00A0'
}

.blockquote-reverse,
blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    text-align: right;
    border-right: 5px solid #eee;
    border-left: 0
}

.blockquote-reverse footer:before,
blockquote.pull-right footer:before,
.blockquote-reverse small:before,
blockquote.pull-right small:before,
.blockquote-reverse .small:before,
blockquote.pull-right .small:before {
    content: ''
}

.blockquote-reverse footer:after,
blockquote.pull-right footer:after,
.blockquote-reverse small:after,
blockquote.pull-right small:after,
.blockquote-reverse .small:after,
blockquote.pull-right .small:after {
    content: '\00A0 \2014'
}

blockquote:before,
blockquote:after {
    content: ""
}

address {
    margin-bottom: 20px;
    font-style: normal;
    line-height: 1.42857143
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace
}

code {
    padding: 2px 4px;
    font-size: 90%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px
}

kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: #fff;
    background-color: #333;
    border-radius: 3px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25)
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    box-shadow: none
}

pre {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 1.42857143;
    color: #333;
    word-break: break-all;
    word-wrap: break-word;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

@media (min-width:768px) {
    .container {
        width: 750px
    }
}

@media (min-width:992px) {
    .container {
        width: 970px
    }
}

@media (min-width:1200px) {
    .container {
        width: 1170px
    }
}

.container-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

.row {
    margin-right: -15px;
    margin-left: -15px
}

.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
    position: relative;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
    float: left
}

.col-xs-12 {
    width: 100%
}

.col-xs-11 {
    width: 91.66666667%
}

.col-xs-10 {
    width: 83.33333333%
}

.col-xs-9 {
    width: 75%
}

.col-xs-8 {
    width: 66.66666667%
}

.col-xs-7 {
    width: 58.33333333%
}

.col-xs-6 {
    width: 50%
}

.col-xs-5 {
    width: 41.66666667%
}

.col-xs-4 {
    width: 33.33333333%
}

.col-xs-3 {
    width: 25%
}

.col-xs-2 {
    width: 16.66666667%
}

.col-xs-1 {
    width: 8.33333333%
}

.col-xs-pull-12 {
    right: 100%
}

.col-xs-pull-11 {
    right: 91.66666667%
}

.col-xs-pull-10 {
    right: 83.33333333%
}

.col-xs-pull-9 {
    right: 75%
}

.col-xs-pull-8 {
    right: 66.66666667%
}

.col-xs-pull-7 {
    right: 58.33333333%
}

.col-xs-pull-6 {
    right: 50%
}

.col-xs-pull-5 {
    right: 41.66666667%
}

.col-xs-pull-4 {
    right: 33.33333333%
}

.col-xs-pull-3 {
    right: 25%
}

.col-xs-pull-2 {
    right: 16.66666667%
}

.col-xs-pull-1 {
    right: 8.33333333%
}

.col-xs-pull-0 {
    right: auto
}

.col-xs-push-12 {
    left: 100%
}

.col-xs-push-11 {
    left: 91.66666667%
}

.col-xs-push-10 {
    left: 83.33333333%
}

.col-xs-push-9 {
    left: 75%
}

.col-xs-push-8 {
    left: 66.66666667%
}

.col-xs-push-7 {
    left: 58.33333333%
}

.col-xs-push-6 {
    left: 50%
}

.col-xs-push-5 {
    left: 41.66666667%
}

.col-xs-push-4 {
    left: 33.33333333%
}

.col-xs-push-3 {
    left: 25%
}

.col-xs-push-2 {
    left: 16.66666667%
}

.col-xs-push-1 {
    left: 8.33333333%
}

.col-xs-push-0 {
    left: auto
}

.col-xs-offset-12 {
    margin-left: 100%
}

.col-xs-offset-11 {
    margin-left: 91.66666667%
}

.col-xs-offset-10 {
    margin-left: 83.33333333%
}

.col-xs-offset-9 {
    margin-left: 75%
}

.col-xs-offset-8 {
    margin-left: 66.66666667%
}

.col-xs-offset-7 {
    margin-left: 58.33333333%
}

.col-xs-offset-6 {
    margin-left: 50%
}

.col-xs-offset-5 {
    margin-left: 41.66666667%
}

.col-xs-offset-4 {
    margin-left: 33.33333333%
}

.col-xs-offset-3 {
    margin-left: 25%
}

.col-xs-offset-2 {
    margin-left: 16.66666667%
}

.col-xs-offset-1 {
    margin-left: 8.33333333%
}

.col-xs-offset-0 {
    margin-left: 0
}

@media (min-width:768px) {
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12 {
        float: left
    }
    .col-sm-12 {
        width: 100%
    }
    .col-sm-11 {
        width: 91.66666667%
    }
    .col-sm-10 {
        width: 83.33333333%
    }
    .col-sm-9 {
        width: 75%
    }
    .col-sm-8 {
        width: 66.66666667%
    }
    .col-sm-7 {
        width: 58.33333333%
    }
    .col-sm-6 {
        width: 50%
    }
    .col-sm-5 {
        width: 41.66666667%
    }
    .col-sm-4 {
        width: 33.33333333%
    }
    .col-sm-3 {
        width: 25%
    }
    .col-sm-2 {
        width: 16.66666667%
    }
    .col-sm-1 {
        width: 8.33333333%
    }
    .col-sm-pull-12 {
        right: 100%
    }
    .col-sm-pull-11 {
        right: 91.66666667%
    }
    .col-sm-pull-10 {
        right: 83.33333333%
    }
    .col-sm-pull-9 {
        right: 75%
    }
    .col-sm-pull-8 {
        right: 66.66666667%
    }
    .col-sm-pull-7 {
        right: 58.33333333%
    }
    .col-sm-pull-6 {
        right: 50%
    }
    .col-sm-pull-5 {
        right: 41.66666667%
    }
    .col-sm-pull-4 {
        right: 33.33333333%
    }
    .col-sm-pull-3 {
        right: 25%
    }
    .col-sm-pull-2 {
        right: 16.66666667%
    }
    .col-sm-pull-1 {
        right: 8.33333333%
    }
    .col-sm-pull-0 {
        right: auto
    }
    .col-sm-push-12 {
        left: 100%
    }
    .col-sm-push-11 {
        left: 91.66666667%
    }
    .col-sm-push-10 {
        left: 83.33333333%
    }
    .col-sm-push-9 {
        left: 75%
    }
    .col-sm-push-8 {
        left: 66.66666667%
    }
    .col-sm-push-7 {
        left: 58.33333333%
    }
    .col-sm-push-6 {
        left: 50%
    }
    .col-sm-push-5 {
        left: 41.66666667%
    }
    .col-sm-push-4 {
        left: 33.33333333%
    }
    .col-sm-push-3 {
        left: 25%
    }
    .col-sm-push-2 {
        left: 16.66666667%
    }
    .col-sm-push-1 {
        left: 8.33333333%
    }
    .col-sm-push-0 {
        left: auto
    }
    .col-sm-offset-12 {
        margin-left: 100%
    }
    .col-sm-offset-11 {
        margin-left: 91.66666667%
    }
    .col-sm-offset-10 {
        margin-left: 83.33333333%
    }
    .col-sm-offset-9 {
        margin-left: 75%
    }
    .col-sm-offset-8 {
        margin-left: 66.66666667%
    }
    .col-sm-offset-7 {
        margin-left: 58.33333333%
    }
    .col-sm-offset-6 {
        margin-left: 50%
    }
    .col-sm-offset-5 {
        margin-left: 41.66666667%
    }
    .col-sm-offset-4 {
        margin-left: 33.33333333%
    }
    .col-sm-offset-3 {
        margin-left: 25%
    }
    .col-sm-offset-2 {
        margin-left: 16.66666667%
    }
    .col-sm-offset-1 {
        margin-left: 8.33333333%
    }
    .col-sm-offset-0 {
        margin-left: 0
    }
}

@media (min-width:992px) {
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
        float: left
    }
    .col-md-12 {
        width: 100%
    }
    .col-md-11 {
        width: 91.66666667%
    }
    .col-md-10 {
        width: 83.33333333%
    }
    .col-md-9 {
        width: 75%
    }
    .col-md-8 {
        width: 66.66666667%
    }
    .col-md-7 {
        width: 58.33333333%
    }
    .col-md-6 {
        width: 50%
    }
    .col-md-5 {
        width: 41.66666667%
    }
    .col-md-4 {
        width: 33.33333333%
    }
    .col-md-3 {
        width: 25%
    }
    .col-md-2 {
        width: 16.66666667%
    }
    .col-md-1 {
        width: 8.33333333%
    }
    .col-md-pull-12 {
        right: 100%
    }
    .col-md-pull-11 {
        right: 91.66666667%
    }
    .col-md-pull-10 {
        right: 83.33333333%
    }
    .col-md-pull-9 {
        right: 75%
    }
    .col-md-pull-8 {
        right: 66.66666667%
    }
    .col-md-pull-7 {
        right: 58.33333333%
    }
    .col-md-pull-6 {
        right: 50%
    }
    .col-md-pull-5 {
        right: 41.66666667%
    }
    .col-md-pull-4 {
        right: 33.33333333%
    }
    .col-md-pull-3 {
        right: 25%
    }
    .col-md-pull-2 {
        right: 16.66666667%
    }
    .col-md-pull-1 {
        right: 8.33333333%
    }
    .col-md-pull-0 {
        right: auto
    }
    .col-md-push-12 {
        left: 100%
    }
    .col-md-push-11 {
        left: 91.66666667%
    }
    .col-md-push-10 {
        left: 83.33333333%
    }
    .col-md-push-9 {
        left: 75%
    }
    .col-md-push-8 {
        left: 66.66666667%
    }
    .col-md-push-7 {
        left: 58.33333333%
    }
    .col-md-push-6 {
        left: 50%
    }
    .col-md-push-5 {
        left: 41.66666667%
    }
    .col-md-push-4 {
        left: 33.33333333%
    }
    .col-md-push-3 {
        left: 25%
    }
    .col-md-push-2 {
        left: 16.66666667%
    }
    .col-md-push-1 {
        left: 8.33333333%
    }
    .col-md-push-0 {
        left: auto
    }
    .col-md-offset-12 {
        margin-left: 100%
    }
    .col-md-offset-11 {
        margin-left: 91.66666667%
    }
    .col-md-offset-10 {
        margin-left: 83.33333333%
    }
    .col-md-offset-9 {
        margin-left: 75%
    }
    .col-md-offset-8 {
        margin-left: 66.66666667%
    }
    .col-md-offset-7 {
        margin-left: 58.33333333%
    }
    .col-md-offset-6 {
        margin-left: 50%
    }
    .col-md-offset-5 {
        margin-left: 41.66666667%
    }
    .col-md-offset-4 {
        margin-left: 33.33333333%
    }
    .col-md-offset-3 {
        margin-left: 25%
    }
    .col-md-offset-2 {
        margin-left: 16.66666667%
    }
    .col-md-offset-1 {
        margin-left: 8.33333333%
    }
    .col-md-offset-0 {
        margin-left: 0
    }
}

@media (min-width:1200px) {
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12 {
        float: left
    }
    .col-lg-12 {
        width: 100%
    }
    .col-lg-11 {
        width: 91.66666667%
    }
    .col-lg-10 {
        width: 83.33333333%
    }
    .col-lg-9 {
        width: 75%
    }
    .col-lg-8 {
        width: 66.66666667%
    }
    .col-lg-7 {
        width: 58.33333333%
    }
    .col-lg-6 {
        width: 50%
    }
    .col-lg-5 {
        width: 41.66666667%
    }
    .col-lg-4 {
        width: 33.33333333%
    }
    .col-lg-3 {
        width: 25%
    }
    .col-lg-2 {
        width: 16.66666667%
    }
    .col-lg-1 {
        width: 8.33333333%
    }
    .col-lg-pull-12 {
        right: 100%
    }
    .col-lg-pull-11 {
        right: 91.66666667%
    }
    .col-lg-pull-10 {
        right: 83.33333333%
    }
    .col-lg-pull-9 {
        right: 75%
    }
    .col-lg-pull-8 {
        right: 66.66666667%
    }
    .col-lg-pull-7 {
        right: 58.33333333%
    }
    .col-lg-pull-6 {
        right: 50%
    }
    .col-lg-pull-5 {
        right: 41.66666667%
    }
    .col-lg-pull-4 {
        right: 33.33333333%
    }
    .col-lg-pull-3 {
        right: 25%
    }
    .col-lg-pull-2 {
        right: 16.66666667%
    }
    .col-lg-pull-1 {
        right: 8.33333333%
    }
    .col-lg-pull-0 {
        right: auto
    }
    .col-lg-push-12 {
        left: 100%
    }
    .col-lg-push-11 {
        left: 91.66666667%
    }
    .col-lg-push-10 {
        left: 83.33333333%
    }
    .col-lg-push-9 {
        left: 75%
    }
    .col-lg-push-8 {
        left: 66.66666667%
    }
    .col-lg-push-7 {
        left: 58.33333333%
    }
    .col-lg-push-6 {
        left: 50%
    }
    .col-lg-push-5 {
        left: 41.66666667%
    }
    .col-lg-push-4 {
        left: 33.33333333%
    }
    .col-lg-push-3 {
        left: 25%
    }
    .col-lg-push-2 {
        left: 16.66666667%
    }
    .col-lg-push-1 {
        left: 8.33333333%
    }
    .col-lg-push-0 {
        left: auto
    }
    .col-lg-offset-12 {
        margin-left: 100%
    }
    .col-lg-offset-11 {
        margin-left: 91.66666667%
    }
    .col-lg-offset-10 {
        margin-left: 83.33333333%
    }
    .col-lg-offset-9 {
        margin-left: 75%
    }
    .col-lg-offset-8 {
        margin-left: 66.66666667%
    }
    .col-lg-offset-7 {
        margin-left: 58.33333333%
    }
    .col-lg-offset-6 {
        margin-left: 50%
    }
    .col-lg-offset-5 {
        margin-left: 41.66666667%
    }
    .col-lg-offset-4 {
        margin-left: 33.33333333%
    }
    .col-lg-offset-3 {
        margin-left: 25%
    }
    .col-lg-offset-2 {
        margin-left: 16.66666667%
    }
    .col-lg-offset-1 {
        margin-left: 8.33333333%
    }
    .col-lg-offset-0 {
        margin-left: 0
    }
}

table {
    background-color: transparent
}

th {
    text-align: left
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 20px
}

.table>thead>tr>th,
.table>tbody>tr>th,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>tbody>tr>td,
.table>tfoot>tr>td {
    padding: 8px;
    line-height: 1.42857143;
    vertical-align: top;
    border-top: 1px solid #ddd
}

.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd
}

.table>caption+thead>tr:first-child>th,
.table>colgroup+thead>tr:first-child>th,
.table>thead:first-child>tr:first-child>th,
.table>caption+thead>tr:first-child>td,
.table>colgroup+thead>tr:first-child>td,
.table>thead:first-child>tr:first-child>td {
    border-top: 0
}

.table>tbody+tbody {
    border-top: 2px solid #ddd
}

.table .table {
    background-color: #fff
}

.table-condensed>thead>tr>th,
.table-condensed>tbody>tr>th,
.table-condensed>tfoot>tr>th,
.table-condensed>thead>tr>td,
.table-condensed>tbody>tr>td,
.table-condensed>tfoot>tr>td {
    padding: 5px
}

.table-bordered {
    border: 1px solid #ddd
}

.table-bordered>thead>tr>th,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>td {
    border: 1px solid #ddd
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td {
    border-bottom-width: 2px
}

.table-striped>tbody>tr:nth-child(odd)>td,
.table-striped>tbody>tr:nth-child(odd)>th {
    background-color: #f9f9f9
}

.table-hover>tbody>tr:hover>td,
.table-hover>tbody>tr:hover>th {
    background-color: #f5f5f5
}

table col[class*=col-] {
    position: static;
    display: table-column;
    float: none
}

table td[class*=col-],
table th[class*=col-] {
    position: static;
    display: table-cell;
    float: none
}

.table>thead>tr>td.active,
.table>tbody>tr>td.active,
.table>tfoot>tr>td.active,
.table>thead>tr>th.active,
.table>tbody>tr>th.active,
.table>tfoot>tr>th.active,
.table>thead>tr.active>td,
.table>tbody>tr.active>td,
.table>tfoot>tr.active>td,
.table>thead>tr.active>th,
.table>tbody>tr.active>th,
.table>tfoot>tr.active>th {
    background-color: #f5f5f5
}

.table-hover>tbody>tr>td.active:hover,
.table-hover>tbody>tr>th.active:hover,
.table-hover>tbody>tr.active:hover>td,
.table-hover>tbody>tr:hover>.active,
.table-hover>tbody>tr.active:hover>th {
    background-color: #e8e8e8
}

.table>thead>tr>td.success,
.table>tbody>tr>td.success,
.table>tfoot>tr>td.success,
.table>thead>tr>th.success,
.table>tbody>tr>th.success,
.table>tfoot>tr>th.success,
.table>thead>tr.success>td,
.table>tbody>tr.success>td,
.table>tfoot>tr.success>td,
.table>thead>tr.success>th,
.table>tbody>tr.success>th,
.table>tfoot>tr.success>th {
    background-color: #dff0d8
}

.table-hover>tbody>tr>td.success:hover,
.table-hover>tbody>tr>th.success:hover,
.table-hover>tbody>tr.success:hover>td,
.table-hover>tbody>tr:hover>.success,
.table-hover>tbody>tr.success:hover>th {
    background-color: #d0e9c6
}

.table>thead>tr>td.info,
.table>tbody>tr>td.info,
.table>tfoot>tr>td.info,
.table>thead>tr>th.info,
.table>tbody>tr>th.info,
.table>tfoot>tr>th.info,
.table>thead>tr.info>td,
.table>tbody>tr.info>td,
.table>tfoot>tr.info>td,
.table>thead>tr.info>th,
.table>tbody>tr.info>th,
.table>tfoot>tr.info>th {
    background-color: #d9edf7
}

.table-hover>tbody>tr>td.info:hover,
.table-hover>tbody>tr>th.info:hover,
.table-hover>tbody>tr.info:hover>td,
.table-hover>tbody>tr:hover>.info,
.table-hover>tbody>tr.info:hover>th {
    background-color: #c4e3f3
}

.table>thead>tr>td.warning,
.table>tbody>tr>td.warning,
.table>tfoot>tr>td.warning,
.table>thead>tr>th.warning,
.table>tbody>tr>th.warning,
.table>tfoot>tr>th.warning,
.table>thead>tr.warning>td,
.table>tbody>tr.warning>td,
.table>tfoot>tr.warning>td,
.table>thead>tr.warning>th,
.table>tbody>tr.warning>th,
.table>tfoot>tr.warning>th {
    background-color: #fcf8e3
}

.table-hover>tbody>tr>td.warning:hover,
.table-hover>tbody>tr>th.warning:hover,
.table-hover>tbody>tr.warning:hover>td,
.table-hover>tbody>tr:hover>.warning,
.table-hover>tbody>tr.warning:hover>th {
    background-color: #faf2cc
}

.table>thead>tr>td.danger,
.table>tbody>tr>td.danger,
.table>tfoot>tr>td.danger,
.table>thead>tr>th.danger,
.table>tbody>tr>th.danger,
.table>tfoot>tr>th.danger,
.table>thead>tr.danger>td,
.table>tbody>tr.danger>td,
.table>tfoot>tr.danger>td,
.table>thead>tr.danger>th,
.table>tbody>tr.danger>th,
.table>tfoot>tr.danger>th {
    background-color: #f2dede
}

.table-hover>tbody>tr>td.danger:hover,
.table-hover>tbody>tr>th.danger:hover,
.table-hover>tbody>tr.danger:hover>td,
.table-hover>tbody>tr:hover>.danger,
.table-hover>tbody>tr.danger:hover>th {
    background-color: #ebcccc
}

@media screen and (max-width:767px) {
    .table-responsive {
        width: 100%;
        margin-bottom: 15px;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: 1px solid #ddd
    }
    .table-responsive>.table {
        margin-bottom: 0
    }
    .table-responsive>.table>thead>tr>th,
    .table-responsive>.table>tbody>tr>th,
    .table-responsive>.table>tfoot>tr>th,
    .table-responsive>.table>thead>tr>td,
    .table-responsive>.table>tbody>tr>td,
    .table-responsive>.table>tfoot>tr>td {
        white-space: nowrap
    }
    .table-responsive>.table-bordered {
        border: 0
    }
    .table-responsive>.table-bordered>thead>tr>th:first-child,
    .table-responsive>.table-bordered>tbody>tr>th:first-child,
    .table-responsive>.table-bordered>tfoot>tr>th:first-child,
    .table-responsive>.table-bordered>thead>tr>td:first-child,
    .table-responsive>.table-bordered>tbody>tr>td:first-child,
    .table-responsive>.table-bordered>tfoot>tr>td:first-child {
        border-left: 0
    }
    .table-responsive>.table-bordered>thead>tr>th:last-child,
    .table-responsive>.table-bordered>tbody>tr>th:last-child,
    .table-responsive>.table-bordered>tfoot>tr>th:last-child,
    .table-responsive>.table-bordered>thead>tr>td:last-child,
    .table-responsive>.table-bordered>tbody>tr>td:last-child,
    .table-responsive>.table-bordered>tfoot>tr>td:last-child {
        border-right: 0
    }
    .table-responsive>.table-bordered>tbody>tr:last-child>th,
    .table-responsive>.table-bordered>tfoot>tr:last-child>th,
    .table-responsive>.table-bordered>tbody>tr:last-child>td,
    .table-responsive>.table-bordered>tfoot>tr:last-child>td {
        border-bottom: 0
    }
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 20px;
    font-size: 21px;
    line-height: inherit;
    color: #333;
    border: 0;
    border-bottom: 1px solid #e5e5e5
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: 700
}

input[type=search] {
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

input[type=radio],
input[type=checkbox] {
    margin: 4px 0 0;
    margin-top: 1px \9;
    line-height: normal
}

input[type=file] {
    display: block
}

input[type=range] {
    display: block;
    width: 100%
}

select[multiple],
select[size] {
    height: auto
}

input[type=file]:focus,
input[type=radio]:focus,
input[type=checkbox]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

output {
    display: block;
    padding-top: 7px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555
}

.form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s
}

.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6)
}

.form-control::-moz-placeholder {
    color: #666d70;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #777
}

.form-control::-webkit-input-placeholder {
    color: #777
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #eee;
    opacity: 1
}

textarea.form-control {
    height: auto
}

input[type=search] {
    -webkit-appearance: none
}

input[type=date],
input[type=time],
input[type=datetime-local],
input[type=month] {
    line-height: 34px;
    line-height: 1.42857143 \0
}

input[type=date].input-sm,
input[type=time].input-sm,
input[type=datetime-local].input-sm,
input[type=month].input-sm {
    line-height: 30px
}

input[type=date].input-lg,
input[type=time].input-lg,
input[type=datetime-local].input-lg,
input[type=month].input-lg {
    line-height: 46px
}

.form-group {
    margin-bottom: 15px
}

.radio,
.checkbox {
    position: relative;
    display: block;
    min-height: 20px;
    margin-top: 10px;
    margin-bottom: 10px
}

.radio label,
.checkbox label {
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: 400;
    cursor: pointer
}

.radio input[type=radio],
.radio-inline input[type=radio],
.checkbox input[type=checkbox],
.checkbox-inline input[type=checkbox] {
    position: absolute;
    margin-top: 4px \9;
    margin-left: -20px
}

.radio+.radio,
.checkbox+.checkbox {
    margin-top: -5px
}

.radio-inline,
.checkbox-inline {
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: 400;
    vertical-align: middle;
    cursor: pointer
}

.radio-inline+.radio-inline,
.checkbox-inline+.checkbox-inline {
    margin-top: 0;
    margin-left: 10px
}

input[type=radio][disabled],
input[type=checkbox][disabled],
input[type=radio].disabled,
input[type=checkbox].disabled,
fieldset[disabled] input[type=radio],
fieldset[disabled] input[type=checkbox] {
    cursor: not-allowed
}

.radio-inline.disabled,
.checkbox-inline.disabled,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox-inline {
    cursor: not-allowed
}

.radio.disabled label,
.checkbox.disabled label,
fieldset[disabled] .radio label,
fieldset[disabled] .checkbox label {
    cursor: not-allowed
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0
}

.form-control-static.input-lg,
.form-control-static.input-sm {
    padding-right: 0;
    padding-left: 0
}

.input-sm,
.form-horizontal .form-group-sm .form-control {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

select.input-sm {
    height: 30px;
    line-height: 30px
}

textarea.input-sm,
select[multiple].input-sm {
    height: auto
}

.input-lg,
.form-horizontal .form-group-lg .form-control {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 6px
}

select.input-lg {
    height: 46px;
    line-height: 46px
}

textarea.input-lg,
select[multiple].input-lg {
    height: auto
}

.has-feedback {
    position: relative
}

.has-feedback .form-control {
    padding-right: 42.5px
}

.form-control-feedback {
    position: absolute;
    top: 25px;
    right: 0;
    z-index: 2;
    display: block;
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center
}

.input-lg+.form-control-feedback {
    width: 46px;
    height: 46px;
    line-height: 46px
}

.input-sm+.form-control-feedback {
    width: 30px;
    height: 30px;
    line-height: 30px
}

.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline {
    color: #3c763d
}

.has-success .form-control {
    border-color: #3c763d;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-success .form-control:focus {
    border-color: #2b542c;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #67b168
}

.has-success .input-group-addon {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #3c763d
}

.has-success .form-control-feedback {
    color: #3c763d
}

.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline {
    color: #8a6d3b
}

.has-warning .form-control {
    border-color: #8a6d3b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-warning .form-control:focus {
    border-color: #66512c;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b
}

.has-warning .input-group-addon {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #8a6d3b
}

.has-warning .form-control-feedback {
    color: #8a6d3b
}

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline {
    color: #a94442
}

.has-error .form-control {
    border-color: #a94442;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075)
}

.has-error .form-control:focus {
    border-color: #843534;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483
}

.has-error .input-group-addon {
    color: #a94442;
    background-color: #f2dede;
    border-color: #a94442
}

.has-error .form-control-feedback {
    color: #a94442
}

.has-feedback label.sr-only~.form-control-feedback {
    top: 0
}

.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #737373
}

@media (min-width:768px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle
    }
    .form-inline .input-group .input-group-addon,
    .form-inline .input-group .input-group-btn,
    .form-inline .input-group .form-control {
        width: auto
    }
    .form-inline .input-group>.form-control {
        width: 100%
    }
    .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .radio,
    .form-inline .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .radio label,
    .form-inline .checkbox label {
        padding-left: 0
    }
    .form-inline .radio input[type=radio],
    .form-inline .checkbox input[type=checkbox] {
        position: relative;
        margin-left: 0
    }
    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
    padding-top: 7px;
    margin-top: 0;
    margin-bottom: 0
}

.form-horizontal .radio,
.form-horizontal .checkbox {
    min-height: 27px
}

.form-horizontal .form-group {
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width:768px) {
    .form-horizontal .control-label {
        padding-top: 7px;
        margin-bottom: 0;
        text-align: right
    }
}

.form-horizontal .has-feedback .form-control-feedback {
    top: 0;
    right: 15px
}

@media (min-width:768px) {
    .form-horizontal .form-group-lg .control-label {
        padding-top: 14.3px
    }
}

@media (min-width:768px) {
    .form-horizontal .form-group-sm .control-label {
        padding-top: 6px
    }
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px
}

.btn:focus,
.btn:active:focus,
.btn.active:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.btn:hover,
.btn:focus {
    color: #333;
    text-decoration: none
}

.btn:active,
.btn.active {
    background-image: none;
    outline: 0;
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
    pointer-events: none;
    cursor: not-allowed;
    filter: alpha(opacity=65);
    box-shadow: none;
    opacity: .65
}

.btn-default {
    color: #333;
    background-color: #fff;
    border-color: #ccc
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open>.dropdown-toggle.btn-default {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad
}

.btn-default:active,
.btn-default.active,
.open>.dropdown-toggle.btn-default {
    background-image: none
}

.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
    background-color: #fff;
    border-color: #ccc
}

.btn-default .badge {
    color: #fff;
    background-color: #333
}

.btn-primary {
    color: #fff;
    background-color: #428bca;
    border-color: #357ebd
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    color: #fff;
    background-color: #3071a9;
    border-color: #285e8e
}

.btn-primary:active,
.btn-primary.active,
.open>.dropdown-toggle.btn-primary {
    background-image: none
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
    background-color: #428bca;
    border-color: #357ebd
}

.btn-primary .badge {
    color: #428bca;
    background-color: #fff
}

.btn-success {
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active,
.btn-success.active,
.open>.dropdown-toggle.btn-success {
    color: #fff;
    background-color: #449d44;
    border-color: #398439
}

.btn-success:active,
.btn-success.active,
.open>.dropdown-toggle.btn-success {
    background-image: none
}

.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
    background-color: #5cb85c;
    border-color: #4cae4c
}

.btn-success .badge {
    color: #5cb85c;
    background-color: #fff
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #46b8da
}

.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc
}

.btn-info:active,
.btn-info.active,
.open>.dropdown-toggle.btn-info {
    background-image: none
}

.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
    background-color: #5bc0de;
    border-color: #46b8da
}

.btn-info .badge {
    color: #5bc0de;
    background-color: #fff
}

.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236
}

.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512
}

.btn-warning:active,
.btn-warning.active,
.open>.dropdown-toggle.btn-warning {
    background-image: none
}

.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
    background-color: #f0ad4e;
    border-color: #eea236
}

.btn-warning .badge {
    color: #f0ad4e;
    background-color: #fff
}

.btn-danger {
    color: #fff;
    background-color: #d9534f;
    border-color: #d43f3a
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925
}

.btn-danger:active,
.btn-danger.active,
.open>.dropdown-toggle.btn-danger {
    background-image: none
}

.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
    background-color: #d9534f;
    border-color: #d43f3a
}

.btn-danger .badge {
    color: #d9534f;
    background-color: #fff
}

.btn-link {
    font-weight: 400;
    color: #428bca;
    cursor: pointer;
    border-radius: 0
}

.btn-link,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
    background-color: transparent;
    box-shadow: none
}

.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
    border-color: transparent
}

.btn-link:hover,
.btn-link:focus {
    color: #2a6496;
    text-decoration: underline;
    background-color: transparent
}

.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
    color: #666d70;
    text-decoration: none
}

.btn-lg,
.btn-group-lg>.btn {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 6px
}

.btn-sm,
.btn-group-sm>.btn {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.btn-xs,
.btn-group-xs>.btn {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: 5px
}

input[type=submit].btn-block,
input[type=reset].btn-block,
input[type=button].btn-block {
    width: 100%
}

.fade {
    opacity: 0;
    -webkit-transition: opacity .15s linear;
    transition: opacity .15s linear
}

.fade.in {
    opacity: 1
}

.collapse {
    display: none
}

.collapse.in {
    display: block
}

tr.collapse.in {
    display: table-row
}

tbody.collapse.in {
    display: table-row-group
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition: height .35s ease;
    transition: height .35s ease
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.dropdown {
    position: relative
}

.dropdown-toggle:focus {
    outline: 0
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175)
}

.dropdown-menu.pull-right {
    right: 0;
    left: auto
}

.dropdown-menu .divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap
}

.dropdown-menu>li>a:hover,
.dropdown-menu>li>a:focus {
    color: #262626;
    text-decoration: none;
    background-color: #f5f5f5
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:hover,
.dropdown-menu>.active>a:focus {
    color: #fff;
    text-decoration: none;
    background-color: #428bca;
    outline: 0
}

.dropdown-menu>.disabled>a,
.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
    color: #777
}

.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent;
    background-image: none;
    filter: progid: DXImageTransform.Microsoft.gradient(enabled=false)
}

.open>.dropdown-menu {
    display: block
}

.open>a {
    outline: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 12px;
    line-height: 1.42857143;
    color: #666d70;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 990
}

.pull-right>.dropdown-menu {
    right: 0;
    left: auto
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
    content: "";
    border-top: 0;
    border-bottom: 4px solid
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 1px
}

@media (min-width:768px) {
    .navbar-right .dropdown-menu {
        right: 0;
        left: auto
    }
    .navbar-right .dropdown-menu-left {
        right: auto;
        left: 0
    }
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.btn-group>.btn,
.btn-group-vertical>.btn {
    position: relative;
    float: left
}

.btn-group>.btn:hover,
.btn-group-vertical>.btn:hover,
.btn-group>.btn:focus,
.btn-group-vertical>.btn:focus,
.btn-group>.btn:active,
.btn-group-vertical>.btn:active,
.btn-group>.btn.active,
.btn-group-vertical>.btn.active {
    z-index: 2
}

.btn-group>.btn:focus,
.btn-group-vertical>.btn:focus {
    outline: 0
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    margin-left: -5px
}

.btn-toolbar .btn-group,
.btn-toolbar .input-group {
    float: left
}

.btn-toolbar>.btn,
.btn-toolbar>.btn-group,
.btn-toolbar>.input-group {
    margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child {
    margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group>.btn-group:first-child>.btn:last-child,
.btn-group>.btn-group:first-child>.dropdown-toggle {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group>.btn-group:last-child>.btn:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn-group>.btn+.dropdown-toggle {
    padding-right: 8px;
    padding-left: 8px
}

.btn-group>.btn-lg+.dropdown-toggle {
    padding-right: 12px;
    padding-left: 12px
}

.btn-group.open .dropdown-toggle {
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125)
}

.btn-group.open .dropdown-toggle.btn-link {
    box-shadow: none
}

.btn .caret {
    margin-left: 0
}

.btn-lg .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0
}

.dropup .btn-lg .caret {
    border-width: 0 5px 5px
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group,
.btn-group-vertical>.btn-group>.btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%
}

.btn-group-vertical>.btn-group>.btn {
    float: none
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 4px
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate
}

.btn-group-justified>.btn,
.btn-group-justified>.btn-group {
    display: table-cell;
    float: none;
    width: 1%
}

.btn-group-justified>.btn-group .btn {
    width: 100%
}

.btn-group-justified>.btn-group .dropdown-menu {
    left: auto
}

[data-toggle=buttons]>.btn>input[type=radio],
[data-toggle=buttons]>.btn>input[type=checkbox] {
    position: absolute;
    z-index: -1;
    filter: alpha(opacity=0);
    opacity: 0
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate
}

.input-group[class*=col-] {
    float: none;
    padding-right: 0;
    padding-left: 0
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0
}

.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 6px
}

select.input-group-lg>.form-control,
select.input-group-lg>.input-group-addon,
select.input-group-lg>.input-group-btn>.btn {
    height: 46px;
    line-height: 46px
}

textarea.input-group-lg>.form-control,
textarea.input-group-lg>.input-group-addon,
textarea.input-group-lg>.input-group-btn>.btn,
select[multiple].input-group-lg>.form-control,
select[multiple].input-group-lg>.input-group-addon,
select[multiple].input-group-lg>.input-group-btn>.btn {
    height: auto
}

.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px
}

select.input-group-sm>.form-control,
select.input-group-sm>.input-group-addon,
select.input-group-sm>.input-group-btn>.btn {
    height: 30px;
    line-height: 30px
}

textarea.input-group-sm>.form-control,
textarea.input-group-sm>.input-group-addon,
textarea.input-group-sm>.input-group-btn>.btn,
select[multiple].input-group-sm>.form-control,
select[multiple].input-group-sm>.input-group-addon,
select[multiple].input-group-sm>.input-group-btn>.btn {
    height: auto
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
    display: table-cell
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0
}

.input-group-addon,
.input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    color: #555;
    text-align: center;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 4px
}

.input-group-addon.input-sm {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px
}

.input-group-addon.input-lg {
    padding: 10px 16px;
    font-size: 18px;
    border-radius: 6px
}

.input-group-addon input[type=radio],
.input-group-addon input[type=checkbox] {
    margin-top: 0
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group>.btn,
.input-group-btn:first-child>.dropdown-toggle,
.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child>.btn-group:not(:last-child)>.btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group-addon:first-child {
    border-right: 0
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group>.btn,
.input-group-btn:last-child>.dropdown-toggle,
.input-group-btn:first-child>.btn:not(:first-child),
.input-group-btn:first-child>.btn-group:not(:first-child)>.btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.input-group-addon:last-child {
    border-left: 0
}

.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap
}

.input-group-btn>.btn {
    position: relative
}

.input-group-btn>.btn+.btn {
    margin-left: -1px
}

.input-group-btn>.btn:hover,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:active {
    z-index: 2
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
    margin-right: -1px
}

.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group {
    margin-left: -1px
}

.nav {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav>li {
    position: relative;
    display: block
}

.nav>li>a {
    position: relative;
    display: block;
    padding: 10px 15px
}

.nav>li>a:hover,
.nav>li>a:focus {
    text-decoration: none;
    background-color: #eee
}

.nav>li.disabled>a {
    color: #777
}

.nav>li.disabled>a:hover,
.nav>li.disabled>a:focus {
    color: #666d70;
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent
}

.nav .open>a,
.nav .open>a:hover,
.nav .open>a:focus {
    background-color: #eee;
    border-color: #428bca
}

.nav .nav-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.nav>li>a>img {
    max-width: none
}

.nav-tabs {
    border-bottom: 1px solid #ddd
}

.nav-tabs>li {
    float: left;
    margin-bottom: -1px
}

.nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0
}

.nav-tabs>li>a:hover {
    border-color: #eee #eee #ddd
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:hover,
.nav-tabs>li.active>a:focus {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 1px solid #ddd;
    border-bottom-color: transparent
}

.nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0
}

.nav-tabs.nav-justified>li {
    float: none
}

.nav-tabs.nav-justified>li>a {
    margin-bottom: 5px;
    text-align: center
}

.nav-tabs.nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto
}

@media (min-width:768px) {
    .nav-tabs.nav-justified>li {
        display: table-cell;
        width: 1%
    }
    .nav-tabs.nav-justified>li>a {
        margin-bottom: 0
    }
}

.nav-tabs.nav-justified>li>a {
    margin-right: 0;
    border-radius: 4px
}

.nav-tabs.nav-justified>.active>a,
.nav-tabs.nav-justified>.active>a:hover,
.nav-tabs.nav-justified>.active>a:focus {
    border: 1px solid #ddd
}

@media (min-width:768px) {
    .nav-tabs.nav-justified>li>a {
        border-bottom: 1px solid #ddd;
        border-radius: 4px 4px 0 0
    }
    .nav-tabs.nav-justified>.active>a,
    .nav-tabs.nav-justified>.active>a:hover,
    .nav-tabs.nav-justified>.active>a:focus {
        border-bottom-color: #fff
    }
}

.nav-pills>li {
    float: left
}

.nav-pills>li>a {
    border-radius: 4px
}

.nav-pills>li+li {
    margin-left: 2px
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:hover,
.nav-pills>li.active>a:focus {
    color: #fff;
    background-color: #428bca
}

.nav-stacked>li {
    float: none
}

.nav-stacked>li+li {
    margin-top: 2px;
    margin-left: 0
}

.nav-justified {
    width: 100%
}

.nav-justified>li {
    float: none
}

.nav-justified>li>a {
    margin-bottom: 5px;
    text-align: center
}

.nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto
}

@media (min-width:768px) {
    .nav-justified>li {
        display: table-cell;
        width: 1%
    }
    .nav-justified>li>a {
        margin-bottom: 0
    }
}

.nav-tabs-justified {
    border-bottom: 0
}

.nav-tabs-justified>li>a {
    margin-right: 0;
    border-radius: 4px
}

.nav-tabs-justified>.active>a,
.nav-tabs-justified>.active>a:hover,
.nav-tabs-justified>.active>a:focus {
    border: 1px solid #ddd
}

@media (min-width:768px) {
    .nav-tabs-justified>li>a {
        border-bottom: 1px solid #ddd;
        border-radius: 4px 4px 0 0
    }
    .nav-tabs-justified>.active>a,
    .nav-tabs-justified>.active>a:hover,
    .nav-tabs-justified>.active>a:focus {
        border-bottom-color: #fff
    }
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.navbar {
    position: relative;
    min-height: 50px;
    margin-bottom: 20px;
    border: 1px solid transparent
}

@media (min-width:768px) {
    .navbar {
        border-radius: 4px
    }
}

@media (min-width:768px) {
    .navbar-header {
        float: left
    }
}

.navbar-collapse {
    padding-right: 15px;
    padding-left: 15px;
    overflow-x: visible;
    -webkit-overflow-scrolling: touch;
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1)
}

.navbar-collapse.in {
    overflow-y: auto
}

@media (min-width:768px) {
    .navbar-collapse {
        width: auto;
        border-top: 0;
        box-shadow: none
    }
    .navbar-collapse.collapse {
        display: block!important;
        height: auto!important;
        padding-bottom: 0;
        overflow: visible!important
    }
    .navbar-collapse.in {
        overflow-y: visible
    }
    .navbar-fixed-top .navbar-collapse,
    .navbar-static-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
        padding-right: 0;
        padding-left: 0
    }
}

.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
    max-height: 340px
}

@media (max-width:480px) and (orientation:landscape) {
    .navbar-fixed-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
        max-height: 200px
    }
}

.container>.navbar-header,
.container-fluid>.navbar-header,
.container>.navbar-collapse,
.container-fluid>.navbar-collapse {
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width:768px) {
    .container>.navbar-header,
    .container-fluid>.navbar-header,
    .container>.navbar-collapse,
    .container-fluid>.navbar-collapse {
        margin-right: 0;
        margin-left: 0
    }
}

.navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px
}

@media (min-width:768px) {
    .navbar-static-top {
        border-radius: 0
    }
}

.navbar-fixed-top,
.navbar-fixed-bottom {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

@media (min-width:768px) {
    .navbar-fixed-top,
    .navbar-fixed-bottom {
        border-radius: 0
    }
}

.navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px
}

.navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0
}

.navbar-brand {
    float: left;
    height: 50px;
    padding: 15px 15px;
    font-size: 18px;
    line-height: 20px
}

.navbar-brand:hover,
.navbar-brand:focus {
    text-decoration: none
}

@media (min-width:768px) {
    .navbar>.container .navbar-brand,
    .navbar>.container-fluid .navbar-brand {
        margin-left: -15px
    }
}

.navbar-toggle {
    position: relative;
    float: right;
    padding: 9px 10px;
    margin-top: 8px;
    margin-right: 15px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px
}

.navbar-toggle:focus {
    outline: 0
}

.navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px
}

.navbar-toggle .icon-bar+.icon-bar {
    margin-top: 4px
}

@media (min-width:768px) {
    .navbar-toggle {
        display: none
    }
}

.navbar-nav {
    margin: 7.5px -15px
}

.navbar-nav>li>a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 20px
}

@media (max-width:767px) {
    .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none
    }
    .navbar-nav .open .dropdown-menu>li>a,
    .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px
    }
    .navbar-nav .open .dropdown-menu>li>a {
        line-height: 20px
    }
    .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-nav .open .dropdown-menu>li>a:focus {
        background-image: none
    }
}

@media (min-width:768px) {
    .navbar-nav {
        float: left;
        margin: 0
    }
    .navbar-nav>li {
        float: left
    }
    .navbar-nav>li>a {
        padding-top: 15px;
        padding-bottom: 15px
    }
    .navbar-nav.navbar-right:last-child {
        margin-right: -15px
    }
}

@media (min-width:768px) {
    .navbar-left {
        float: left!important
    }
    .navbar-right {
        float: right!important
    }
}

.navbar-form {
    padding: 10px 15px;
    margin-top: 8px;
    margin-right: -15px;
    margin-bottom: 8px;
    margin-left: -15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .1), 0 1px 0 rgba(255, 255, 255, .1)
}

@media (min-width:768px) {
    .navbar-form .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle
    }
    .navbar-form .input-group .input-group-addon,
    .navbar-form .input-group .input-group-btn,
    .navbar-form .input-group .form-control {
        width: auto
    }
    .navbar-form .input-group>.form-control {
        width: 100%
    }
    .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .radio,
    .navbar-form .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .radio label,
    .navbar-form .checkbox label {
        padding-left: 0
    }
    .navbar-form .radio input[type=radio],
    .navbar-form .checkbox input[type=checkbox] {
        position: relative;
        margin-left: 0
    }
    .navbar-form .has-feedback .form-control-feedback {
        top: 0
    }
}

@media (max-width:767px) {
    .navbar-form .form-group {
        margin-bottom: 5px
    }
}

@media (min-width:768px) {
    .navbar-form {
        width: auto;
        padding-top: 0;
        padding-bottom: 0;
        margin-right: 0;
        margin-left: 0;
        border: 0;
        box-shadow: none
    }
    .navbar-form.navbar-right:last-child {
        margin-right: -15px
    }
}

.navbar-nav>li>.dropdown-menu {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.navbar-btn {
    margin-top: 8px;
    margin-bottom: 8px
}

.navbar-btn.btn-sm {
    margin-top: 10px;
    margin-bottom: 10px
}

.navbar-btn.btn-xs {
    margin-top: 14px;
    margin-bottom: 14px
}

.navbar-text {
    margin-top: 15px;
    margin-bottom: 15px
}

@media (min-width:768px) {
    .navbar-text {
        float: left;
        margin-right: 15px;
        margin-left: 15px
    }
    .navbar-text.navbar-right:last-child {
        margin-right: 0
    }
}

.navbar-default {
    background-color: #f8f8f8;
    border-color: #e7e7e7
}

.navbar-default .navbar-brand {
    color: #777
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
    color: #5e5e5e;
    background-color: transparent
}

.navbar-default .navbar-text {
    color: #777
}

.navbar-default .navbar-nav>li>a {
    color: #777
}

.navbar-default .navbar-nav>li>a:hover,
.navbar-default .navbar-nav>li>a:focus {
    color: #333;
    background-color: transparent
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.active>a:hover,
.navbar-default .navbar-nav>.active>a:focus {
    color: #555;
    background-color: #e7e7e7
}

.navbar-default .navbar-nav>.disabled>a,
.navbar-default .navbar-nav>.disabled>a:hover,
.navbar-default .navbar-nav>.disabled>a:focus {
    color: #ccc;
    background-color: transparent
}

.navbar-default .navbar-toggle {
    border-color: #ddd
}

.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
    background-color: #ddd
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: #888
}

.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
    border-color: #e7e7e7
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:hover,
.navbar-default .navbar-nav>.open>a:focus {
    color: #555;
    background-color: #e7e7e7
}

@media (max-width:767px) {
    .navbar-default .navbar-nav .open .dropdown-menu>li>a {
        color: #777
    }
    .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>li>a:focus {
        color: #333;
        background-color: transparent
    }
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a,
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus {
        color: #555;
        background-color: #e7e7e7
    }
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus {
        color: #ccc;
        background-color: transparent
    }
}

.navbar-default .navbar-link {
    color: #777
}

.navbar-default .navbar-link:hover {
    color: #333
}

.navbar-default .btn-link {
    color: #777
}

.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
    color: #333
}

.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:focus {
    color: #ccc
}

.navbar-inverse {
    background-color: #222;
    border-color: #080808
}

.navbar-inverse .navbar-brand {
    color: #777
}

.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-text {
    color: #777
}

.navbar-inverse .navbar-nav>li>a {
    color: #777
}

.navbar-inverse .navbar-nav>li>a:hover,
.navbar-inverse .navbar-nav>li>a:focus {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-nav>.active>a,
.navbar-inverse .navbar-nav>.active>a:hover,
.navbar-inverse .navbar-nav>.active>a:focus {
    color: #fff;
    background-color: #080808
}

.navbar-inverse .navbar-nav>.disabled>a,
.navbar-inverse .navbar-nav>.disabled>a:hover,
.navbar-inverse .navbar-nav>.disabled>a:focus {
    color: #444;
    background-color: transparent
}

.navbar-inverse .navbar-toggle {
    border-color: #333
}

.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
    background-color: #333
}

.navbar-inverse .navbar-toggle .icon-bar {
    background-color: #fff
}

.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
    border-color: #101010
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:hover,
.navbar-inverse .navbar-nav>.open>a:focus {
    color: #fff;
    background-color: #080808
}

@media (max-width:767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
        border-color: #080808
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
        background-color: #080808
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
        color: #777
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus {
        color: #fff;
        background-color: transparent
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus {
        color: #fff;
        background-color: #080808
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus {
        color: #444;
        background-color: transparent
    }
}

.navbar-inverse .navbar-link {
    color: #777
}

.navbar-inverse .navbar-link:hover {
    color: #fff
}

.navbar-inverse .btn-link {
    color: #777
}

.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
    color: #fff
}

.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:focus {
    color: #444
}

.breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    list-style: none;
    background-color: #f5f5f5;
    border-radius: 4px
}

.breadcrumb>li {
    display: inline-block
}

.breadcrumb>li+li:before {
    padding: 0 5px;
    color: #ccc;
    content: "/\00a0"
}

.breadcrumb>.active {
    color: #666d70
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px
}

.pagination>li {
    display: inline
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #428bca;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
    margin-left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px
}

.pagination>li>a:hover,
.pagination>li>span:hover,
.pagination>li>a:focus,
.pagination>li>span:focus {
    color: #2a6496;
    background-color: #eee;
    border-color: #ddd
}

.pagination>.active>a,
.pagination>.active>span,
.pagination>.active>a:hover,
.pagination>.active>span:hover,
.pagination>.active>a:focus,
.pagination>.active>span:focus {
    z-index: 2;
    color: #fff;
    cursor: default;
    background-color: #428bca;
    border-color: #428bca
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
    color: #666d70;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #ddd
}

.pagination-lg>li>a,
.pagination-lg>li>span {
    padding: 10px 16px;
    font-size: 18px
}

.pagination-lg>li:first-child>a,
.pagination-lg>li:first-child>span {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.pagination-lg>li:last-child>a,
.pagination-lg>li:last-child>span {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.pagination-sm>li>a,
.pagination-sm>li>span {
    padding: 5px 10px;
    font-size: 12px
}

.pagination-sm>li:first-child>a,
.pagination-sm>li:first-child>span {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px
}

.pagination-sm>li:last-child>a,
.pagination-sm>li:last-child>span {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px
}

.pager {
    padding-left: 0;
    margin: 20px 0;
    text-align: center;
    list-style: none
}

.pager li {
    display: inline
}

.pager li>a,
.pager li>span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px
}

.pager li>a:hover,
.pager li>a:focus {
    text-decoration: none;
    background-color: #eee
}

.pager .next>a,
.pager .next>span {
    float: right
}

.pager .previous>a,
.pager .previous>span {
    float: left
}

.pager .disabled>a,
.pager .disabled>a:hover,
.pager .disabled>a:focus,
.pager .disabled>span {
    color: #666d70;
    cursor: not-allowed;
    background-color: #fff
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

a.label:hover,
a.label:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.label:empty {
    display: none
}

.btn .label {
    position: relative;
    top: -1px
}

.label-default {
    background-color: #777
}

.label-default[href]:hover,
.label-default[href]:focus {
    background-color: #5e5e5e
}

.label-primary {
    background-color: #428bca
}

.label-primary[href]:hover,
.label-primary[href]:focus {
    background-color: #3071a9
}

.label-success {
    background-color: #5cb85c
}

.label-success[href]:hover,
.label-success[href]:focus {
    background-color: #449d44
}

.label-info {
    background-color: #5bc0de
}

.label-info[href]:hover,
.label-info[href]:focus {
    background-color: #31b0d5
}

.label-warning {
    background-color: #f0ad4e
}

.label-warning[href]:hover,
.label-warning[href]:focus {
    background-color: #ec971f
}

.label-danger {
    background-color: #d9534f
}

.label-danger[href]:hover,
.label-danger[href]:focus {
    background-color: #c9302c
}

.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    background-color: #666d70;
    border-radius: 10px
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.btn-xs .badge {
    top: 0;
    padding: 1px 5px
}

a.badge:hover,
a.badge:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

a.list-group-item.active>.badge,
.nav-pills>.active>a>.badge {
    color: #428bca;
    background-color: #fff
}

.nav-pills>li>a>.badge {
    margin-left: 3px
}

.jumbotron {
    padding: 30px;
    margin-bottom: 30px;
    color: inherit;
    background-color: #eee
}

.jumbotron h1,
.jumbotron .h1 {
    color: inherit
}

.jumbotron p {
    margin-bottom: 15px;
    font-size: 21px;
    font-weight: 200
}

.jumbotron>hr {
    border-top-color: #d5d5d5
}

.container .jumbotron {
    border-radius: 6px
}

.jumbotron .container {
    max-width: 100%
}

@media screen and (min-width:768px) {
    .jumbotron {
        padding-top: 48px;
        padding-bottom: 48px
    }
    .container .jumbotron {
        padding-right: 60px;
        padding-left: 60px
    }
    .jumbotron h1,
    .jumbotron .h1 {
        font-size: 63px
    }
}

.thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 20px;
    line-height: 1.42857143;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out
}

.thumbnail>img,
.thumbnail a>img {
    margin-right: auto;
    margin-left: auto
}

a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
    border-color: #428bca
}

.thumbnail .caption {
    padding: 9px;
    color: #333
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px
}

.alert h4 {
    margin-top: 0;
    color: inherit
}

.alert .alert-link {
    font-weight: 700
}

.alert>p,
.alert>ul {
    margin-bottom: 0
}

.alert>p+p {
    margin-top: 5px
}

.alert-dismissable,
.alert-dismissible {
    padding-right: 35px
}

.alert-dismissable .close,
.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6
}

.alert-success hr {
    border-top-color: #c9e2b3
}

.alert-success .alert-link {
    color: #2b542c
}

.alert-info {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1
}

.alert-info hr {
    border-top-color: #a6e1ec
}

.alert-info .alert-link {
    color: #245269
}

.alert-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc
}

.alert-warning hr {
    border-top-color: #f7e1b5
}

.alert-warning .alert-link {
    color: #66512c
}

.alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1
}

.alert-danger hr {
    border-top-color: #e4b9c0
}

.alert-danger .alert-link {
    color: #843534
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 40px 0
    }
    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 40px 0
    }
    to {
        background-position: 0 0
    }
}

.progress {
    height: 20px;
    margin-bottom: 20px;
    overflow: hidden;
    background-color: #f5f5f5;
    border-radius: 4px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1)
}

.progress-bar {
    float: left;
    width: 0;
    height: 100%;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    background-color: #428bca;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
    -webkit-transition: width .6s ease;
    transition: width .6s ease
}

.progress-striped .progress-bar,
.progress-bar-striped {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 40px 40px
}

.progress.active .progress-bar,
.progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite
}

.progress-bar[aria-valuenow="1"],
.progress-bar[aria-valuenow="2"] {
    min-width: 30px
}

.progress-bar[aria-valuenow="0"] {
    min-width: 30px;
    color: #666d70;
    background-color: transparent;
    background-image: none;
    box-shadow: none
}

.progress-bar-success {
    background-color: #5cb85c
}

.progress-striped .progress-bar-success {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-info {
    background-color: #5bc0de
}

.progress-striped .progress-bar-info {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-warning {
    background-color: #f0ad4e
}

.progress-striped .progress-bar-warning {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.progress-bar-danger {
    background-color: #d9534f
}

.progress-striped .progress-bar-danger {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent)
}

.media,
.media-body {
    overflow: hidden;
    zoom: 1
}

.media,
.media .media {
    margin-top: 15px
}

.media:first-child {
    margin-top: 0
}

.media-object {
    display: block
}

.media-heading {
    margin: 0 0 5px
}

.media>.pull-left {
    margin-right: 10px
}

.media>.pull-right {
    margin-left: 10px
}

.media-list {
    padding-left: 0;
    list-style: none
}

.list-group {
    padding-left: 0;
    margin-bottom: 20px
}

.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd
}

.list-group-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px
}

.list-group-item>.badge {
    float: right
}

.list-group-item>.badge+.badge {
    margin-right: 5px
}

a.list-group-item {
    color: #555
}

a.list-group-item .list-group-item-heading {
    color: #333
}

a.list-group-item:hover,
a.list-group-item:focus {
    color: #555;
    text-decoration: none;
    background-color: #f5f5f5
}

.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
    color: #666d70;
    background-color: #eee
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
    color: inherit
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
    color: #777
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
    z-index: 2;
    color: #fff;
    background-color: #428bca;
    border-color: #428bca
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active .list-group-item-heading>small,
.list-group-item.active:hover .list-group-item-heading>small,
.list-group-item.active:focus .list-group-item-heading>small,
.list-group-item.active .list-group-item-heading>.small,
.list-group-item.active:hover .list-group-item-heading>.small,
.list-group-item.active:focus .list-group-item-heading>.small {
    color: inherit
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
    color: #e1edf7
}

.list-group-item-success {
    color: #3c763d;
    background-color: #dff0d8
}

a.list-group-item-success {
    color: #3c763d
}

a.list-group-item-success .list-group-item-heading {
    color: inherit
}

a.list-group-item-success:hover,
a.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6
}

a.list-group-item-success.active,
a.list-group-item-success.active:hover,
a.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d
}

.list-group-item-info {
    color: #31708f;
    background-color: #d9edf7
}

a.list-group-item-info {
    color: #31708f
}

a.list-group-item-info .list-group-item-heading {
    color: inherit
}

a.list-group-item-info:hover,
a.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3
}

a.list-group-item-info.active,
a.list-group-item-info.active:hover,
a.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f
}

.list-group-item-warning {
    color: #8a6d3b;
    background-color: #fcf8e3
}

a.list-group-item-warning {
    color: #8a6d3b
}

a.list-group-item-warning .list-group-item-heading {
    color: inherit
}

a.list-group-item-warning:hover,
a.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b
}

.list-group-item-danger {
    color: #a94442;
    background-color: #f2dede
}

a.list-group-item-danger {
    color: #a94442
}

a.list-group-item-danger .list-group-item-heading {
    color: inherit
}

a.list-group-item-danger:hover,
a.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442
}

.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3
}

.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}

.panel-body {
    padding: 15px
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px
}

.panel-heading>.dropdown .dropdown-toggle {
    color: inherit
}

.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    color: inherit
}

.panel-title>a {
    color: inherit
}

.panel-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.panel>.list-group {
    margin-bottom: 0
}

.panel>.list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0
}

.panel>.list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px
}

.panel>.list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.panel-heading+.list-group .list-group-item:first-child {
    border-top-width: 0
}

.list-group+.panel-footer {
    border-top-width: 0
}

.panel>.table,
.panel>.table-responsive>.table,
.panel>.panel-collapse>.table {
    margin-bottom: 0
}

.panel>.table:first-child,
.panel>.table-responsive:first-child>.table:first-child {
    border-top-left-radius: 3px;
    border-top-right-radius: 3px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child {
    border-top-left-radius: 3px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child {
    border-top-right-radius: 3px
}

.panel>.table:last-child,
.panel>.table-responsive:last-child>.table:last-child {
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
    border-bottom-left-radius: 3px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
    border-bottom-right-radius: 3px
}

.panel>.panel-body+.table,
.panel>.panel-body+.table-responsive {
    border-top: 1px solid #ddd
}

.panel>.table>tbody:first-child>tr:first-child th,
.panel>.table>tbody:first-child>tr:first-child td {
    border-top: 0
}

.panel>.table-bordered,
.panel>.table-responsive>.table-bordered {
    border: 0
}

.panel>.table-bordered>thead>tr>th:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,
.panel>.table-bordered>tbody>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,
.panel>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-bordered>thead>tr>td:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,
.panel>.table-bordered>tbody>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,
.panel>.table-bordered>tfoot>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child {
    border-left: 0
}

.panel>.table-bordered>thead>tr>th:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,
.panel>.table-bordered>tbody>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,
.panel>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-bordered>thead>tr>td:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,
.panel>.table-bordered>tbody>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,
.panel>.table-bordered>tfoot>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child {
    border-right: 0
}

.panel>.table-bordered>thead>tr:first-child>td,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,
.panel>.table-bordered>tbody>tr:first-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,
.panel>.table-bordered>thead>tr:first-child>th,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,
.panel>.table-bordered>tbody>tr:first-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th {
    border-bottom: 0
}

.panel>.table-bordered>tbody>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,
.panel>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-bordered>tbody>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,
.panel>.table-bordered>tfoot>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th {
    border-bottom: 0
}

.panel>.table-responsive {
    margin-bottom: 0;
    border: 0
}

.panel-group {
    margin-bottom: 20px
}

.panel-group .panel {
    margin-bottom: 0;
    border-radius: 4px
}

.panel-group .panel+.panel {
    margin-top: 5px
}

.panel-group .panel-heading {
    border-bottom: 0
}

.panel-group .panel-heading+.panel-collapse>.panel-body {
    border-top: 1px solid #ddd
}

.panel-group .panel-footer {
    border-top: 0
}

.panel-group .panel-footer+.panel-collapse .panel-body {
    border-bottom: 1px solid #ddd
}

.panel-default {
    border-color: #ddd
}

.panel-default>.panel-heading {
    color: #333;
    background-color: #f5f5f5;
    border-color: #ddd
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #ddd
}

.panel-default>.panel-heading .badge {
    color: #f5f5f5;
    background-color: #333
}

.panel-default>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #ddd
}

.panel-primary {
    border-color: #428bca
}

.panel-primary>.panel-heading {
    color: #fff;
    background-color: #428bca;
    border-color: #428bca
}

.panel-primary>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #428bca
}

.panel-primary>.panel-heading .badge {
    color: #428bca;
    background-color: #fff
}

.panel-primary>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #428bca
}

.panel-success {
    border-color: #d6e9c6
}

.panel-success>.panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6
}

.panel-success>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #d6e9c6
}

.panel-success>.panel-heading .badge {
    color: #dff0d8;
    background-color: #3c763d
}

.panel-success>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #d6e9c6
}

.panel-info {
    border-color: #bce8f1
}

.panel-info>.panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1
}

.panel-info>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #bce8f1
}

.panel-info>.panel-heading .badge {
    color: #d9edf7;
    background-color: #31708f
}

.panel-info>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #bce8f1
}

.panel-warning {
    border-color: #faebcc
}

.panel-warning>.panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc
}

.panel-warning>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #faebcc
}

.panel-warning>.panel-heading .badge {
    color: #fcf8e3;
    background-color: #8a6d3b
}

.panel-warning>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #faebcc
}

.panel-danger {
    border-color: #ebccd1
}

.panel-danger>.panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1
}

.panel-danger>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #ebccd1
}

.panel-danger>.panel-heading .badge {
    color: #f2dede;
    background-color: #a94442
}

.panel-danger>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #ebccd1
}

.embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
}

.embed-responsive.embed-responsive-16by9 {
    padding-bottom: 56.25%
}

.embed-responsive.embed-responsive-4by3 {
    padding-bottom: 75%
}

.well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05)
}

.well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, .15)
}

.well-lg {
    padding: 24px;
    border-radius: 6px
}

.well-sm {
    padding: 9px;
    border-radius: 3px
}

.close {
    float: right;
    font-size: 21px;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    filter: alpha(opacity=20);
    opacity: .2
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    filter: alpha(opacity=50);
    opacity: .5
}

button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0
}

.modal-open {
    overflow: hidden
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.modal.fade .modal-dialog {
    -webkit-transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    -webkit-transform: translate3d(0, -25%, 0);
    transform: translate3d(0, -25%, 0)
}

.modal.in .modal-dialog {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px
}

.modal-content {
    position: relative;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #999;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 6px;
    outline: 0;
    box-shadow: 0 3px 9px rgba(0, 0, 0, .5)
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000
}

.modal-backdrop.fade {
    filter: alpha(opacity=0);
    opacity: 0
}

.modal-backdrop.in {
    filter: alpha(opacity=50);
    opacity: .5
}

.modal-header {
    min-height: 16.43px;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5
}

.modal-header .close {
    margin-top: -2px
}

.modal-title {
    margin: 0;
    line-height: 1.42857143
}

.modal-body {
    position: relative;
    padding: 15px
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5
}

.modal-footer .btn+.btn {
    margin-bottom: 0;
    margin-left: 5px
}

.modal-footer .btn-group .btn+.btn {
    margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
    margin-left: 0
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width:768px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto
    }
    .modal-content {
        box-shadow: 0 5px 15px rgba(0, 0, 0, .5)
    }
    .modal-sm {
        width: 300px
    }
}

@media (min-width:992px) {
    .modal-lg {
        width: 900px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-size: 12px;
    line-height: 1.4;
    visibility: visible;
    filter: alpha(opacity=0);
    opacity: 0
}

.tooltip.in {
    filter: alpha(opacity=90);
    opacity: .9
}

.tooltip.top {
    padding: 5px 0;
    margin-top: -3px
}

.tooltip.right {
    padding: 0 5px;
    margin-left: 3px
}

.tooltip.bottom {
    padding: 5px 0;
    margin-top: 3px
}

.tooltip.left {
    padding: 0 5px;
    margin-left: -3px
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    text-decoration: none;
    background-color: #000;
    border-radius: 4px
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-left .tooltip-arrow {
    bottom: 0;
    left: 5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-right .tooltip-arrow {
    right: 5px;
    bottom: 0;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-left .tooltip-arrow {
    top: 0;
    left: 5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-right .tooltip-arrow {
    top: 0;
    right: 5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: none;
    max-width: 276px;
    padding: 1px;
    text-align: left;
    white-space: normal;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 6px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2)
}

.popover.top {
    margin-top: -10px
}

.popover.right {
    margin-left: 10px
}

.popover.bottom {
    margin-top: 10px
}

.popover.left {
    margin-left: -10px
}

.popover-title {
    padding: 8px 14px;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-radius: 5px 5px 0 0
}

.popover-content {
    padding: 9px 14px
}

.popover>.arrow,
.popover>.arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover>.arrow {
    border-width: 11px
}

.popover>.arrow:after {
    content: "";
    border-width: 10px
}

.popover.top>.arrow {
    bottom: -11px;
    left: 50%;
    margin-left: -11px;
    border-top-color: #999;
    border-top-color: rgba(0, 0, 0, .25);
    border-bottom-width: 0
}

.popover.top>.arrow:after {
    bottom: 1px;
    margin-left: -10px;
    content: " ";
    border-top-color: #fff;
    border-bottom-width: 0
}

.popover.right>.arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-right-color: #999;
    border-right-color: rgba(0, 0, 0, .25);
    border-left-width: 0
}

.popover.right>.arrow:after {
    bottom: -10px;
    left: 1px;
    content: " ";
    border-right-color: #fff;
    border-left-width: 0
}

.popover.bottom>.arrow {
    top: -11px;
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0, 0, 0, .25)
}

.popover.bottom>.arrow:after {
    top: 1px;
    margin-left: -10px;
    content: " ";
    border-top-width: 0;
    border-bottom-color: #fff
}

.popover.left>.arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: #999;
    border-left-color: rgba(0, 0, 0, .25)
}

.popover.left>.arrow:after {
    right: 1px;
    bottom: -10px;
    content: " ";
    border-right-width: 0;
    border-left-color: #fff
}

.carousel {
    position: relative
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden
}

.carousel-inner>.item {
    position: relative;
    display: none;
    -webkit-transition: .6s ease-in-out left;
    transition: .6s ease-in-out left
}

.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
    line-height: 1
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 15%;
    font-size: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6);
    filter: alpha(opacity=50);
    opacity: .5
}

.carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)), to(rgba(0, 0, 0, .0001)));
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, .5) 0, rgba(0, 0, 0, .0001) 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
    background-repeat: repeat-x
}

.carousel-control.right {
    right: 0;
    left: auto;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .0001)), to(rgba(0, 0, 0, .5)));
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, .0001) 0, rgba(0, 0, 0, .5) 100%);
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
    background-repeat: repeat-x
}

.carousel-control:hover,
.carousel-control:focus {
    color: #fff;
    text-decoration: none;
    filter: alpha(opacity=90);
    outline: 0;
    opacity: .9
}

.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block
}

.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px
}

.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px
}

.carousel-control .icon-prev,
.carousel-control .icon-next {
    width: 20px;
    height: 20px;
    margin-top: -10px;
    font-family: serif
}

.carousel-control .icon-prev:before {
    content: '\2039'
}

.carousel-control .icon-next:before {
    content: '\203a'
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 15;
    width: 60%;
    padding-left: 0;
    margin-left: -30%;
    text-align: center;
    list-style: none
}

.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: rgba(0, 0, 0, 0);
    border: 1px solid #fff;
    border-radius: 10px
}

.carousel-indicators .active {
    width: 12px;
    height: 12px;
    margin: 0;
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, .6)
}

.carousel-caption .btn {
    text-shadow: none
}

@media screen and (min-width:768px) {
    .carousel-control .glyphicon-chevron-left,
    .carousel-control .glyphicon-chevron-right,
    .carousel-control .icon-prev,
    .carousel-control .icon-next {
        width: 30px;
        height: 30px;
        margin-top: -15px;
        font-size: 30px
    }
    .carousel-control .glyphicon-chevron-left,
    .carousel-control .icon-prev {
        margin-left: -15px
    }
    .carousel-control .glyphicon-chevron-right,
    .carousel-control .icon-next {
        margin-right: -15px
    }
    .carousel-caption {
        right: 20%;
        left: 20%;
        padding-bottom: 30px
    }
    .carousel-indicators {
        bottom: 20px
    }
}

.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after,
.form-horizontal .form-group:before,
.form-horizontal .form-group:after,
.btn-toolbar:before,
.btn-toolbar:after,
.btn-group-vertical>.btn-group:before,
.btn-group-vertical>.btn-group:after,
.nav:before,
.nav:after,
.navbar:before,
.navbar:after,
.navbar-header:before,
.navbar-header:after,
.navbar-collapse:before,
.navbar-collapse:after,
.pager:before,
.pager:after,
.panel-body:before,
.panel-body:after,
.modal-footer:before,
.modal-footer:after {
    display: table;
    content: " "
}

.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical>.btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.pager:after,
.panel-body:after,
.modal-footer:after {
    clear: both
}

.center-block {
    display: block;
    margin-right: auto;
    margin-left: auto
}

.pull-right {
    float: right!important
}

.pull-left {
    float: left!important
}

.hide {
    display: none!important
}

.show {
    display: block!important
}

.invisible {
    visibility: hidden
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.hidden {
    display: none!important;
    visibility: hidden!important
}

.affix {
    position: fixed;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

@-ms-viewport {
    width: device-width
}

.visible-xs,
.visible-sm,
.visible-md,
.visible-lg {
    display: none!important
}

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
    display: none!important
}

@media (max-width:767px) {
    .visible-xs {
        display: block!important
    }
    table.visible-xs {
        display: table
    }
    tr.visible-xs {
        display: table-row!important
    }
    th.visible-xs,
    td.visible-xs {
        display: table-cell!important
    }
}

@media (max-width:767px) {
    .visible-xs-block {
        display: block!important
    }
}

@media (max-width:767px) {
    .visible-xs-inline {
        display: inline!important
    }
}

@media (max-width:767px) {
    .visible-xs-inline-block {
        display: inline-block!important
    }
}

@media (min-width:768px) and (max-width:991px) {
    .visible-sm {
        display: block!important
    }
    table.visible-sm {
        display: table
    }
    tr.visible-sm {
        display: table-row!important
    }
    th.visible-sm,
    td.visible-sm {
        display: table-cell!important
    }
}

@media (min-width:768px) and (max-width:991px) {
    .visible-sm-block {
        display: block!important
    }
}

@media (min-width:768px) and (max-width:991px) {
    .visible-sm-inline {
        display: inline!important
    }
}

@media (min-width:768px) and (max-width:991px) {
    .visible-sm-inline-block {
        display: inline-block!important
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .visible-md {
        display: block!important
    }
    table.visible-md {
        display: table
    }
    tr.visible-md {
        display: table-row!important
    }
    th.visible-md,
    td.visible-md {
        display: table-cell!important
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .visible-md-block {
        display: block!important
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .visible-md-inline {
        display: inline!important
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .visible-md-inline-block {
        display: inline-block!important
    }
}

@media (min-width:1200px) {
    .visible-lg {
        display: block!important
    }
    table.visible-lg {
        display: table
    }
    tr.visible-lg {
        display: table-row!important
    }
    th.visible-lg,
    td.visible-lg {
        display: table-cell!important
    }
}

@media (min-width:1200px) {
    .visible-lg-block {
        display: block!important
    }
}

@media (min-width:1200px) {
    .visible-lg-inline {
        display: inline!important
    }
}

@media (min-width:1200px) {
    .visible-lg-inline-block {
        display: inline-block!important
    }
}

@media (max-width:767px) {
    .hidden-xs {
        display: none!important
    }
}

@media (min-width:768px) and (max-width:991px) {
    .hidden-sm {
        display: none!important
    }
}

@media (min-width:992px) and (max-width:1199px) {
    .hidden-md {
        display: none!important
    }
}

@media (min-width:1200px) {
    .hidden-lg {
        display: none!important
    }
}

.visible-print {
    display: none!important
}

@media print {
    .visible-print {
        display: block!important
    }
    table.visible-print {
        display: table
    }
    tr.visible-print {
        display: table-row!important
    }
    th.visible-print,
    td.visible-print {
        display: table-cell!important
    }
}

.visible-print-block {
    display: none!important
}

@media print {
    .visible-print-block {
        display: block!important
    }
}

.visible-print-inline {
    display: none!important
}

@media print {
    .visible-print-inline {
        display: inline!important
    }
}

.visible-print-inline-block {
    display: none!important
}

@media print {
    .visible-print-inline-block {
        display: inline-block!important
    }
}

@media print {
    .hidden-print {
        display: none!important
    }
}
/* 
 * 	Core Owl Carousel CSS File
 *	v1.3.3
 */

/* clearfix */
.owl-carousel .owl-wrapper:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
/* display none until init */
.owl-carousel{
	display: none;
	position: relative;
	width: 100%;
	-ms-touch-action: pan-y;
}
.owl-carousel .owl-wrapper{
	display: none;
	position: relative;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-wrapper-outer{
	overflow: hidden;
	position: relative;
	width: 100%;
}
.owl-carousel .owl-wrapper-outer.autoHeight{
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
	
.owl-carousel .owl-item{
	float: left;
}
.owl-controls .owl-page,
.owl-controls .owl-buttons div{
	cursor: pointer;
}
.owl-controls {
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* mouse grab icon */
.grabbing { 
    cursor:url(grabbing.png) 8 8, move;
}

/* fix */
.owl-carousel  .owl-wrapper,
.owl-carousel  .owl-item{
	-webkit-backface-visibility: hidden;
	-moz-backface-visibility:    hidden;
	-ms-backface-visibility:     hidden;
  -webkit-transform: translate3d(0,0,0);
  -moz-transform: translate3d(0,0,0);
  -ms-transform: translate3d(0,0,0);
}


/*!
 * dotcentric-nav v1.0.3
 * http://www.dotcentric.co.uk
 * Copyright (c) dotcentric 2014
 */

html {
	height: 100%;
}

body {
	position: relative;
	height: 100%;
}

.dcnav-container {
	overflow: hidden;
	width: 100%;
}

.dcnav-main {
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative;
}

.dcnav {
	position: relative;
	clear: both;
}

.dcnav * {
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}

.dcnav .is-hidden {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}

.dcnav-wrapper {
	position: relative;
}

.dcnav-menu {
	position: relative;
	list-style: none;
	padding: 0;
	margin: 0;
}

.dcnav-link,
.dcnav-back,
.dcnav-close {
	display: block;
	cursor: pointer;
	position: relative;
}

.dcnav-link {
	word-wrap: break-word;
	word-break: break-word;
}

.dcnav-item {
	display: block;
}

.dcnav-item > .dcnav-menu {
	display: none;
}

.dcnav-title {
	font-size: inherit;
	font-weight: normal;
	margin: 0;
}

/*!
 * dotcentric-nav (mobile)
 */

@media screen and (max-width: 991px) {
	.dcnav-main {
		z-index: 1;
		position: relative;
		min-height: 100%;
	}
	.dcnav-root {
		position: relative;
		left: 0;
		-webkit-transition: left 400ms ease;
		transition: left 400ms ease;
	}
	.dcnav-root.is-nav-active .dcnav {
		z-index: 90000;
		opacity: 1;
	}
	.dcnav-root.is-nav-active .dcnav-menu.is-active {
		-webkit-overflow-scrolling: touch;
	}
	.dcnav-root.is-nav.is-nav-effect-push,
	.dcnav-root.is-nav.is-nav-effect-behind {
		left: 260px;
	}
	.dcnav-root.is-nav .dcnav {
		pointer-events: all;
		left: 0;
	}
	.dcnav {
		z-index: 90000;
		overflow: hidden;
		position: absolute;
		position: fixed;
		top: 0;
		left: -260px;
		bottom: 0;
		width: 100%;
		height: 100%;
		max-width: 260px;
		-webkit-transition: left 400ms ease;
		transition: left 400ms ease;
		pointer-events: none;
		opacity: 0;
	}
	.dcnav.is-effect-push {
		left: -260px;
		width: 260px;
	}
	.dcnav.is-effect-behind {
		z-index: 0 !important;
		left: 0 !important;
	}
	.dcnav .is-desktop {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
	.dcnav-wrapper {
		position: absolute;
		padding: 0;
		margin: 0;
		top: 0;
		bottom: 0;
		width: 100%;
		-webkit-transition: left 400ms ease;
		transition: left 400ms ease;
	}
	.dcnav-back,
	.dcnav-close,
	.dcnav-link {
		width: 100%;
	}
	.dcnav-menu {
		height: 100%;
	}
	.dcnav-menu.is-active {
		overflow-y: auto;
		overflow-x: hidden;
	}
	.dcnav-item {
		clear: both;
	}
	.dcnav-item .dcnav-menu {
		position: absolute;
		top: 0;
		left: 100%;
		width: 100%;
	}
	.dcnav-item.is-active > .dcnav-menu {
		display: block;
		height: 100%;
	}
	.dcnav.is-effect-top {
		-webkit-transition: none;
		transition: none;
		position: relative;
		left: auto;
		width: auto;
		height: auto;
		max-width: none;
		margin-left: 15px;
		margin-right: 15px;
	}
	.dcnav.is-effect-top .dcnav-close {
		display: none;
	}
	.dcnav.is-effect-top .dcnav-menu {
		height: auto !important;
	}
	.dcnav.is-effect-top .dcnav-wrapper {
		position: relative;
		top: 0;
		height: 0;
		-webkit-transition: left 400ms ease, height 400ms ease;
		transition: left 400ms ease, height 400ms ease;
	}
	.no-js .dcnav-main {
		height: auto;
		min-height: 0;
	}
	.no-js .dcnav,
	.no-js .dcnav-wrapper {
		position: relative;
		height: auto;
		top: auto;
		left: auto;
		max-width: none;
		pointer-events: all;
		opacity: 1;
	}
	.no-js .dcnav-back,
	.no-js .dcnav-title {
		display: none;
	}
	.no-js .dcnav-item.is-ajax {
		display: none;
	}
	.no-js .dcnav-menu {
		position: relative;
		display: block;
		top: auto;
		left: auto;
	}
}

/*!
 * dotcentric-nav (desktop)
 */

button.drop {
	display: none !important;
}

@media screen and (min-width: 992px) {
	.dcnav {
		z-index: 90000;
	}
	.dcnav .is-mobile {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
	.dcnav-close,
	.dcnav-back,
	.dcnav-title {
		display: none !important;
	}
	.dcnav-wrapper {
		padding: 0;
		left: auto !important;
		min-height: 0 !important;
		height: auto !important;
	}
	.dcnav-menu {
		*zoom: 1;
	}
	.dcnav-menu:after {
		clear: both;
	}
	.dcnav-item {
		position: relative;
		float: left;
	}
	.dcnav-item.is-ajax > .dcnav-menu {
		display: none;
	}
	.dcnav-item .dcnav-item {
		float: none;
	}
	.dcnav-item .dcnav-menu {
		position: absolute;
		top: 100%;
		left: 0;
	}
	.dcnav-item .dcnav-menu .dcnav-menu {
		display: none !important;
	}
	.dcnav-item:hover > .dcnav-menu {
		display: block;
	}
	.dcnav-link {
		padding: 10px 15px 10px 30px !important;
	}
	.dcnav-link.showdrop {
		background: #eff0f0;
		color: #00334c;
	}
	.dcnav-link button.drop.active {
		background-color: #eff0f0 !important;
	}
	.dcnav-link.showdrop + .dcnav-menu {
		display: block;
	}
	.dcnav-link:focus {
		text-decoration: none !important;
	}
	.dcnav-wrapper > .dcnav-menu > .dcnav-item {
		padding-right: 32px;
	}
	.dcnav-wrapper > .dcnav-menu > .dcnav-item > .dcnav-link button.drop {
		display: block !important;
		width: 32px;
		height: 100%;
		background: transparent;
		border: 0;
		background-image: url('/static/img/refresh/chevron-white.svg');
		background-position: center;
		background-repeat: no-repeat;
		background-size: 8px 14px;
		transform: rotate(90deg);
		position: absolute;
		bottom: 0;
		top: 0;
		right: -32px;
	}

	.dcnav-wrapper > .dcnav-menu > .dcnav-item:hover > .dcnav-link button.drop {
		transform: rotate(270deg);
		background-image: url('/static/img/refresh/chevron.svg');
	}

	.dcnav-wrapper > .dcnav-menu > .dcnav-item > .dcnav-link button.drop:focus,
	.dcnav-wrapper > .dcnav-menu > .dcnav-item > .dcnav-link.showdrop button.drop {
		background-color: #eff0f0 !important;
		background-image: url('/static/img/refresh/chevron.svg');
	}
}
.dcnav-wrapper > .dcnav-menu > .dcnav-item > .dcnav-link:hover button.drop {
	background-image: url('/static/img/refresh/chevron.svg');
	transform: rotate(270deg);
}

@-webkit-viewport {
	width: device-width;
}

@-moz-viewport {
	width: device-width;
}

@-ms-viewport {
	width: device-width;
}

@-o-viewport {
	width: device-width;
}

@viewport {
	width: device-width;
}

/*!
 * Copyright (c) dotcentric | http://www.dotcentric.co.uk
 */

/**!
 * Base Styles
 */

@font-face {
	font-family: 'Gotham book';
	src: url('/static/fonts/Gotham/gothambook-webfont.woff2') format('woff2'),
		url('/static/fonts/Gotham/gothambook-webfont.woff') format('woff'),
		url('/static/fonts/Gotham/gothambook-webfont.ttf') format('truetype');
}

@font-face {
	font-family: 'Gotham bold';
	src: url('/static/fonts/Gotham/gotham-bold-webfont.woff2') format('woff2'),
		url('/static/fonts/Gotham/gotham-bold-webfont.woff') format('woff'),
		url('/static/fonts/Gotham/gotham-bold-webfont.ttf') format('truetype');
}

@font-face {
	font-family: 'Gotham black';
	src: url('/static/fonts/Gotham/gotham-black-webfont.woff2') format('woff2'),
		url('/static/fonts/Gotham/gotham-black-webfont.woff') format('woff'),
		url('/static/fonts/Gotham/gotham-black-webfont.ttf') format('truetype');
}

* {
	font-weight: normal !important;
}

strong,
b {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.fc-dark-green {
	color: #005961;
}

.bg-dark-green {
	background-color: #005961;
}

.fc-light-green {
	color: #74e0c1;
}

.bg-light-green {
	background-color: #74e0c1;
}

.fc-very-light-green {
	color: #eafbf6;
}

.bg-very-light-green {
	background: #eafbf6;
}

.fs-16 {
	font-size: 16px;
}

html,
body {
	height: 100%;
	color: #111111;
	font-family: 'Gotham book', Arial, sans-serif;
	line-height: 1.35;
}

body {
	position: relative;
	min-width: 320px;
	background: #d1d3d4;
}

img {
	display: block;
	max-width: 100%;
	height: auto;
}

.js *[data-href] {
	cursor: pointer;
}

.js .js-expand {
	position: relative;
	z-index: 101;
}

.js .js-expand:after {
	position: absolute;
	display: block;
	content: '';
	right: 15px;
	top: 50%;
	width: 0;
	height: 0;
	margin-top: -9px;
	border: 5px solid transparent;
	border-bottom-color: #111111;
}

.js .js-expand.is-collapsed:after {
	margin-top: -3px;
	border-bottom-color: transparent;
	border-top-color: #111111;
}

.no-js .is-js {
	display: none !important;
	visibility: hidden;
}

@media only screen {
	.is-print {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
}

.container {
	width: auto !important;
	max-width: 1500px;
	min-width: 280px;
}

.container > .row {
	max-width: 1000px;
	margin: 0 auto 20px auto;
}

.container > .row:last-child {
	margin-bottom: 0;
}

@media screen and (max-width: 991px) {
	.container .row > [class*='col-'] {
		margin-bottom: 20px;
	}
	.container .row > [class*='col-']:last-child {
		margin-bottom: 0;
	}
}

@media screen and (max-width: 767px) {
	.container {
		padding-left: 0;
		padding-right: 0;
	}
}

.modal {
	z-index: 6001;
}

.modal-backdrop {
	z-index: 6000;
}

.modal-close {
	z-index: 6010;
	-webkit-appearance: none;
	-moz-appearance: none;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	outline: none;
	position: absolute;
	top: 10px;
	right: 20px;
	width: 40px;
	height: 40px;
	padding: 10px;
	background: transparent;
	outline: none;
	border: none;
	display: none;
}

.modal .modal-close {
	display: block;
}

.modal-close svg {
	width: 20px;
	height: 20px;
	fill: #31006f;
}

.modal-close:hover svg {
	fill: #d72882;
}

.lt-ie9 .modal-close,
.no-inline-svg .modal-close {
	background: #666d70;
}

.lt-ie9 .modal-close:hover,
.no-inline-svg .modal-close:hover {
	background: #d72882;
}

@media screen and (max-width: 767px) {
	.modal-close {
		top: 5px;
		right: 5px;
	}
}

.media {
	margin-top: 0;
}

.media p:last-child {
	margin-bottom: 0;
}

.media > .pull-left {
	margin-right: 15px;
}

.media > .pull-right {
	margin-left: 15px;
}

.list-group {
	border-color: #d1d3d4;
	border-radius: 0;
}

.list-group-item {
	border-color: #d1d3d4;
	border-radius: 0 !important;
}

.well {
	box-shadow: none;
	background-color: #eff0f0;
	border-color: #d1d3d4;
	border-radius: 0;
}

.header-notice .notice {
	background: #fbe9f2;
}

.header-notice .notice .btn {
	color: #111;
}

.alert {
	border-radius: 0;
}

.alert p {
	font-size: 14px;
}

.alert-error {
	color: #ffffff;
	border: 0;
	background-color: #d00000;
}

.alert-danger {
	color: #d00000;
	border-color: #d00000;
	background-color: rgba(214, 63, 2, 0.1);
	border-width: 2px;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.aside {
	background-color: #eafbf6;
	margin-bottom: 15px;
	padding: 15px;
}

.aside:last-child {
	margin-bottom: 0;
}

.aside p {
	font-size: 16px;
}

.aside .btn-block {
	max-width: 280px;
}

.aside .prose .small {
	font-size: 14px;
}

.aside .prose .pull-left {
	clear: left;
	max-width: 30px;
	margin-right: 10px;
	margin-bottom: 5px;
}

.aside-thumb {
	margin: 5px 0;
}

.module {
	margin-bottom: 20px;
}

.module:last-child {
	margin-bottom: 0;
}

@media screen and (max-width: 767px) {
	.aside {
		margin-left: -15px;
		margin-right: -15px;
		padding-left: 15px;
		padding-right: 15px;
		border-left: 0;
		border-right: 0;
	}
}

a {
	color: #31006f;
	-webkit-transition: color 100ms ease;
	transition: color 100ms ease;
}

a:hover,
a:focus {
	color: #d72882;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
	margin: 20px 0 10px 0;
	font-weight: normal !important;
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #111111;
}

h1 {
	font-family: 'Gotham black', Arial, sans-serif;
}

.prose h1 {
	color: #111111;
}

h1:first-child,
h2:first-child,
h3:first-child,
h4:first-child,
h5:first-child,
h6:first-child,
.h1:first-child,
.h2:first-child,
.h3:first-child,
.h4:first-child,
.h5:first-child,
.h6:first-child {
	margin-top: 0;
}

/*
.main h1 a,
.main h2 a,
.main h3 a,
.main h4 a,
.main h5 a,
.main h6 a,
.main .h1 a,
.main .h2 a,
.main .h3 a,
.main .h4 a,
.main .h5 a,
.main .h6 a {
    text-decoration: none !important;
    color: #31006F !important;
}
*/

.main h1 a:hover,
.main h1 a:focus,
.main h2 a:hover,
.main h2 a:focus,
.main h3 a:hover,
.main h3 a:focus,
.main h4 a:hover,
.main h4 a:focus,
.main h5 a:hover,
.main h5 a:focus,
.main h6 a:hover,
.main h6 a:focus,
.main .h1 a:hover,
.main .h1 a:focus,
.main .h2 a:hover,
.main .h2 a:focus,
.main .h3 a:hover,
.main .h3 a:focus,
.main .h4 a:hover,
.main .h4 a:focus,
.main .h5 a:hover,
.main .h5 a:focus,
.main .h6 a:hover,
.main .h6 a:focus {
	color: #31006f !important;
}

/* h1 time,
h1 strong,
h2 time,
h2 strong,
h3 time,
h3 strong,
h4 time,
h4 strong,
h5 time,
h5 strong,
h6 time,
h6 strong,
.h1 time,
.h1 strong,
.h2 time,
.h2 strong,
.h3 time,
.h3 strong,
.h4 time,
.h4 strong,
.h5 time,
.h5 strong,
.h6 time,
.h6 strong {
	color: #666d70;
} */

h1,
.h1 {
	font-size: 40px;
}

h2,
.h2 {
	font-size: 30px;
}

h3,
.h3 {
	font-size: 24px;
}

h4,
.h4 {
	font-size: 20px;
}

h5,
.h5 {
	font-size: 18px;
}

h6,
.h6 {
	font-size: 18px;
}

p {
	font-size: 18px;
}

p a {
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #d72882;
	text-decoration: underline;
}

p a:hover,
p a:focus {
	text-decoration: underline !important;
	color: #31006f;
}

p:last-child {
	margin-bottom: 0;
}

.more-link,
.return-link {
	display: inline-block;
	font-size: 16px;
	line-height: 20px;
	text-decoration: none;
}

.more-link span,
.return-link span {
	display: inline-block;
	text-decoration: underline;
}

.more-link:hover,
.return-link:hover,
.more-line:hover span,
.return-link:hover span {
	text-decoration: none;
}

.learnmore li:hover .more-link,
.learnmore li:hover .return-link,
.learnmore li:hover .more-line span,
.learnmore li:hover .return-link span {
	text-decoration: none;
}

.more-link svg,
.return-link svg {
	display: inline-block;
	vertical-align: middle;
	width: 10px;
	height: 10px;
	fill: #d72882;
}

.w-link,
.xl-link,
.pdf-link,
.external-link,
.html-link,
.arrow-link {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.w-link:before,
.xl-link:before,
.pdf-link:before,
.external-link:before,
.html-link:before,
.arrow-link:before {
	display: inline-block;
	vertical-align: top;
	font-size: inherit;
	content: '';
	background-size: 100% auto;
	background-repeat: no-repeat;
	background-position: center center;
	height: 22px;
	margin-right: 6px;
	width: 18px;
}

.arrow-link:before {
	background-image: url('/static/img/refresh/chevron.svg');
	background-size: 12px 12px;
}

.no-svg .arrow-link:before {
	background-image: url('/static/img/refresh/chevron.png');
}

.w-link:before {
	background-image: url('/static/img/files/w.svg');
}

.no-svg .w-link:before {
	background-image: url('/static/img/files/w.png');
}

.xl-link:before {
	background-image: url('/static/img/files/xl.svg');
}

.no-svg .xl-link:before {
	background-image: url('/static/img/files/xl.png');
}

.pdf-link:before {
	background-image: url('/static/img/refresh/pdf-icon-pink.svg');
}

.share .pdf-link:before {
	background-image: url('/static/img/refresh/pdf2.svg');
}

.no-svg .pdf-link:before {
	background-image: url('/static/img/files/pdf.png');
}

.html-link:before {
	background-image: url('/static/img/refresh/read-icon.svg');
	width: 27px;
	height: 20px;
}

.no-svg .html-link:before {
	background-image: url('/static/img/files/pdf.png');
}

.external-link:before {
	background-image: url('/static/img/refresh/open-window-icon-pink.svg');
}
.external-link:hover:before {
	background-image: url('/static/img/refresh/open-window-icon-dark.svg');
}

.pl-0 {
	padding-left: 0 !important;
}

.lead {
	font-size: 24px;
	margin-bottom: 20px;
	line-height: auto;
}

*[class*='col-'] > .lead {
	margin-bottom: 20px;
}

small {
	font-size: 14px;
}

time {
	display: block;
	font-size: 18px;
	color: #666d70;
	font-family: 'Gotham bold', Arial, sans-serif;
}

time + h1,
time + h2,
time + h3 {
	margin-top: 10px !important;
}

.prose {
	margin-bottom: 30px;
}

.prose:last-child {
	margin-bottom: 0;
}

.prose > ul:not([class]),
.prose > ol:not([class]) {
	font-size: 18px;
	clear: both;
}

.prose a {
	color: #d72882;
	text-decoration: underline;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.prose a:hover,
.prose a:focus {
	color: #31006f;
	text-decoration: underline;
}

.searched-list .prose a {
	text-decoration: none;
}

.searched-list .prose a:hover,
.searched-list .prose a:focus {
	text-decoration: underline;
}

.prose .media {
	margin: 20px 0;
}

.prose img {
	margin-bottom: 15px;
}

.prose img.pull-left {
	margin-right: 15px;
	max-width: 40%;
}

.prose img.pull-right {
	margin-left: 15px;
	max-width: 40%;
}

.prose img.media-object {
	margin-bottom: 0;
}

hr {
	border-color: #d1d3d4;
}

.hr-bold {
	background: #eff0f0;
	margin: 30px 0;
	height: 5px;
	border: 0;
}

.hr-medium {
	background: #b2b6b7;
	margin: 30px 0;
	height: 2px;
	border: 0;
}

.prose table,
.table {
	margin: 20px 0;
	font-size: 14px;
}

.prose table p,
.prose table ul,
.prose table ol,
.table p,
.table ul,
.table ol {
	font-size: 14px;
}

.prose table a,
.table a {
	text-decoration: underline;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.prose table a:hover,
.prose table a:focus,
.table a:hover,
.table a:focus {
	text-decoration: none;
}

.prose table .more-link,
.table .more-link {
	text-decoration: none;
}

.prose table > thead > tr > th,
.prose table > thead > tr > td,
.prose table > tbody > tr > th,
.prose table > tbody > tr > td,
.prose table > tfoot > tr > th,
.prose table > tfoot > tr > td,
.table > thead > tr > th,
.table > thead > tr > td,
.table > tbody > tr > th,
.table > tbody > tr > td,
.table > tfoot > tr > th,
.table > tfoot > tr > td {
	padding: 8px;
	line-height: 1.4;
	vertical-align: top;
	border-top: 1px solid #d1d3d4;
}

/* .prose table > thead,
.table > thead {
	color: #ffffff;
	background: #666d70;
} */

.prose table .th-light,
.table .th-light {
	color: #111111;
	background: #eff0f0;
}

.prose table .th-dark,
.table .th-dark {
	color: #ffffff;
	background: #666d70;
}

.prose table > thead > tr > th,
.table > thead > tr > th {
	vertical-align: top;
}

.table-responsive {
	margin: 20px 0;
}

.table-responsive .table {
	margin: 0;
}

.prose table,
.table-bordered {
	border: 1px solid #d1d3d4;
}

.prose table > thead > tr > th,
.prose table > thead > tr > td,
.prose table > tbody > tr > th,
.prose table > tbody > tr > td,
.prose table > tfoot > tr > th,
.prose table > tfoot > tr > td,
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > th,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > th,
.table-bordered > tfoot > tr > td {
	border: 1px solid #d1d3d4;
}

.prose table > thead > tr > th,
.prose table > thead > tr > td,
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
	border-bottom-width: 1px;
	border-color: #d1d3d4;
}

.prose table > tbody > tr:nth-child(odd) > td,
.table-striped > tbody > tr:nth-child(odd) > td {
	background: transparent;
}

.prose table > tbody > tr:nth-child(even),
.prose table > tbody > tr:nth-child(even) > td,
.table-striped > tbody > tr:nth-child(even),
.table-striped > tbody > tr:nth-child(even) > td {
	background: #ebfbf6;
}

@media screen and (min-width: 992px) {
	.prose table,
	.table {
		font-size: 16px;
	}
	.prose table p,
	.prose table ul,
	.prose table ol,
	.table p,
	.table ul,
	.table ol {
		font-size: 16px;
	}
	.prose table thead,
	.table thead {
		font-size: 16px;
	}
	.table-small {
		font-size: 14px;
	}
	.table-small p,
	.table-small ul,
	.table-small ol,
	.table-small .th-light {
		font-size: 14px;
	}
}

@media screen and (max-width: 767px) {
	.table.is-responsive thead,
	.table.is-responsive tbody,
	.table.is-responsive th,
	.table.is-responsive td,
	.table.is-responsive tr {
		display: block;
	}
	.table.is-responsive thead {
		display: none;
	}
	.table.is-responsive.table-striped td {
		background: #ffffff;
	}
	.table.is-responsive.table-striped .th-dark td,
	.table.is-responsive.table-striped .th-light td {
		background: transparent;
	}
	.table.is-responsive tr {
		border-bottom: 1px solid #d1d3d4;
	}
	.table.is-responsive tr:last-child {
		border-bottom: 0;
	}
	.table.is-responsive td {
		position: relative;
		width: 100%;
		border: 0;
		border-bottom: 1px solid #d1d3d4;
		white-space: normal;
		text-align: left;
	}
	.table.is-responsive td:last-child {
		border-bottom: 0;
	}
	.table.is-responsive td[data-title] {
		width: 100%;
		padding-right: 0;
		padding-left: 35%;
		overflow: hidden;
	}
	.table.is-responsive td[data-title]:before {
		z-index: 100;
		position: relative;
		display: block;
		float: left;
		width: 53.84615%;
		margin-left: -53.84615%;
		margin-top: -8px;
		margin-bottom: -8px;
		height: 100%;
		padding: 8px 16px 8px 8px;
		text-align: left;
		font-family: 'Gotham bold', Arial, sans-serif;
		content: attr(data-title);
	}
	.table.is-responsive td[data-title]:after {
		position: absolute;
		display: block;
		content: '';
		top: 0;
		left: 0;
		bottom: 0;
		right: 65%;
		margin-right: 8px;
		background: #eff0f0;
		border-right: 1px solid #d1d3d4;
	}
}

.file {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 26px;
	height: 22px;
}

.btn .file {
	margin-right: 4px;
}

.file-sprite {
	display: inline-block;
	vertical-align: top;
	width: 23px;
	height: 24px;
}

.file-icon {
	display: block;
	width: 18px;
	height: 22px;
	margin-left: 8px;
	fill: #111111;
}

.file-reversed .file-icon {
	fill: #ffffff;
}

.file-text {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	position: absolute;
	display: block;
	padding: 1px 2px;
	top: 8px;
	left: 0;
	height: 10px;
	font-size: 9px;
	line-height: 10px;
	min-width: 16px;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-family: sans-serif;
	text-transform: uppercase;
	background: #666d70;
	color: #ffffff;
}

.file-pdf .file-text {
	background: #ff0000;
}

.file-w .file-text {
	background: #0084b3;
}

.file-xl .file-text {
	background: #438516;
}

.tags {
	margin: 15px 0;
}

.tags:last-child {
	margin-bottom: 0;
}

.tags p {
	display: inline;
	margin: 0;
}

.tags p + ul {
	margin-left: 5px;
}

.tags ul {
	display: inline;
	list-style: none;
	padding: 0;
	margin: 0;
}

.tags li {
	display: inline;
	margin: 0;
}

.tags a {
	display: inline-block;
	background: #eff0f0;
	border: 1px solid #d1d3d4;
	border-radius: 10px;
	padding: 4px 8px;
	line-height: 1;
	font-size: 14px;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-decoration: none;
	color: #31006f;
}

.tags a:hover {
	color: #d72882;
}

.required {
	color: #b44113;
}

.btn {
	border: 0;
	border-radius: 0;
	-webkit-transition: opacity 150ms ease, background-color 150ms ease;
	transition: opacity 150ms ease, background-color 150ms ease;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 18px;
	text-decoration: none !important;
	padding: 7px 20px;
}

.btn:hover {
	text-decoration: none !important;
}

.btn:active,
.btn.active {
	box-shadow: none;
}

/* .btn:hover,
.btn:focus {
	opacity: 0.8;
	outline: none;
	border: none;
} */

.btn-flat {
	color: #31006f;
	background: #fff;
}

.btn-flat:before {
	display: inline-block;
	content: '';
	background: url('/static/img/refresh/chevron.svg') no-repeat;
	transform: rotate(180deg);
	background-size: contain;
	width: 8px;
	height: 14px;
	margin-right: 7px;
}

.btn-flat:hover,
.btn-flat:focus {
	background: #fff;
	color: #31006f;
	opacity: 1;
	text-decoration: underline;
}

.btn-default {
	background-color: #eff0f0;
}

.btn-primary {
	background-color: #d72882;
}

.btn-primary:hover,
.btn-primary:focus {
	background-color: #31006f;
}

.btn-secondary {
	background-color: #74e0c1;
	color: #111111;
}

.btn-secondary:hover,
.btn-secondary:focus {
	background-color: #ffffff;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
	background-color: #d72882;
	border-color: #d72882;
}

.btn-block svg,
.btn-block img {
	display: inline-block;
	vertical-align: top;
	width: 20px;
	height: 20px;
	fill: #ffffff;
	margin: -2px 2px 0 -6px;
}

.btn-secondary.btn-block svg {
	fill: #111111;
}

.btn-primary.btn-block svg {
	fill: #ffffff;
}

.btn-block img {
	margin: 0;
}

.btn-rss {
	background-color: #b44113 !important;
}

.btn-rss svg,
.btn-rss img {
	width: 25px;
}

.btn-more {
	display: block;
	width: 100%;
	font-size: 18px;
	line-height: 25px;
	padding: 10px 15px;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-align: center;
	color: #111111;
	border: 1px solid #c6dab9;
	background-color: #ebfbf6;
}

.btn-more em {
	color: #111111;
	font-weight: normal;
	font-style: normal;
}

.btn-more img {
	display: inline-block;
	vertical-align: top;
	margin: 0 5px;
}

.btn-more:hover {
	color: #111111;
	background-color: #74e0c1;
}

.btn-more:hover em {
	color: #111111;
}

.btn-more.is-loading {
	color: #111111;
	background-color: #74e0c1;
}

.btn-more.is-loading em {
	color: #111111;
}

.help-block {
	font-size: 14px;
}

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline {
	color: #d00000;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.form {
	font-size: 16px;
}

.form.module {
	margin-bottom: 30px;
}

.form > .alert {
	margin: 0;
}

.form > .alert + .alert {
	border-top: 0;
}

.form > .alert + .form-body {
	border-top: 0;
}

.form-body {
	padding: 15px;
	border: 1px solid #d1d3d4;
}

.form-body + .form-footer {
	border-top: 0;
}

.form-body + .alert {
	margin-top: -1px;
}

.form-body + .notice {
	margin-top: -1px;
}

.form-body > .form-group {
	margin-bottom: 15px;
}

.form-body > .form-group:last-child {
	margin-bottom: 0;
}

.form-footer {
	overflow: hidden;
	border: 1px solid #d1d3d4;
	padding: 15px;
}

.form-footer + .alert {
	margin-top: -1px;
}

.form-footer .pull-left {
	font-size: 16px;
	margin: 10px 0 0 0;
}

.radio-group .radio {
	display: inline-block;
	margin-right: 15px;
}

.checkbox-group .checkbox {
	display: inline-block;
	margin-right: 15px;
}

label {
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 16px;
}

.form-control {
	border-radius: 0;
	border-color: #666d70;
	height: 36px;
	color: #111111;
}

.form-control:focus {
	border-width: 1px;
	border-color: #111 !important;
	box-shadow: none;
}

.form-control.has-error,
.has-error .form-control {
	border-color: #d00000;
	background-color: #fae5e5;
	border-width: 2px;
}

.form-control + .error {
	margin-top: 5px;
}

.form-placeholder-label {
	display: none;
	margin: 0;
}

.lt-ie9 .form-placeholder-label,
.lt-ie10 .form-placeholder-label {
	display: block;
	line-height: 18px;
}

.btn-lg,
.btn-group-lg > .btn {
	padding: 10px 16px;
	font-size: 18px;
	line-height: 20px;
}

@media screen and (min-width: 768px) {
	.btn-block {
		padding: 10px 16px;
		font-size: 18px;
		line-height: 20px;
	}
}

.confirm-form {
	*zoom: 1;
	padding: 20px;
}

.confirm-form:before,
.confirm-form:after {
	content: '';
	display: table;
}

.confirm-form:after {
	clear: both;
}

.confirm-form .form-title {
	font-size: 18px;
	margin-bottom: 20px;
	padding-right: 30px;
	line-height: 25px;
}

.form-title {
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-weight: normal;
}

.confirm-form .notice-toggle {
	position: absolute;
	display: block;
	top: 15px;
	right: 15px;
	width: 20px;
	height: 20px;
}

.confirm-form .notice-toggle svg {
	width: 20px;
	height: 20px;
	fill: #666d70;
}

.confirm-form .notice {
	margin-bottom: 20px;
	display: none;
}

.no-js .confirm-form .notice,
.no-js .confirm-form .notice-toggle {
	display: none !important;
	visibility: hidden;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.confirm-form {
		padding: 30px;
		border-bottom: 1px solid #d1d3d4;
		background-color: #eff0f0;
	}
	[class*='col-'] > .confirm-form {
		margin-left: -30px;
		margin-right: -30px;
	}
	.confirm-form .notice-toggle {
		top: 30px;
	}
	.confirm-form .btn-block,
	.confirm-form .form-group {
		width: 25%;
		float: left;
		margin: 0;
	}
	.lt-ie10 .confirm-form .btn-block {
		margin-top: 18px;
	}
	.confirm-form .form-group {
		padding-right: 5px;
	}
	.confirm-form .form-control {
		height: 40px;
		font-size: 16px;
		line-height: 26px;
	}
	.confirm-form .form-control.has-error {
		line-height: 24px;
	}
}

@media screen and (max-width: 767px) {
	.confirm-form {
		padding: 20px 15px;
		border-bottom: 1px solid #d1d3d4;
	}
	[class*='col-'] > .confirm-form {
		margin-left: -15px;
		margin-right: -15px;
	}
	.confirm-form .form-title {
		font-size: 18px;
	}
}

@media screen and (min-width: 992px) {
	.confirm-form {
		position: relative;
		background: #ffffff;
		background: rgba(255, 255, 255, 0.8);
	}
	.confirm-form .notice-toggle {
		position: absolute;
		top: 20px;
		left: 20px;
		right: auto;
	}
	.confirm-form .notice {
		position: absolute;
		top: 0;
		right: 100%;
		width: 450px;
		margin-right: 15px;
		box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.2);
	}
	.confirm-form .notice:after {
		position: absolute;
		display: block;
		content: '';
		top: 15px;
		left: 100%;
		width: 0;
		height: 0;
		border-left: 15px solid #fffbcc;
		border-top: 15px solid transparent;
		border-bottom: 15px solid transparent;
	}
	.confirm-form .form-title {
		padding-right: 0;
		padding-left: 30px;
	}
}

@media screen and (min-width: 768px) {
	.inline-form .form-body {
		display: table;
		width: 100%;
		padding-left: 10px;
		padding-right: 10px;
	}
	.inline-form .form-control {
		height: 40px;
		font-size: 16px;
		line-height: 26px;
	}
	.inline-form .form-control.has-error {
		line-height: 24px;
	}
	.inline-form .form-group {
		display: table-cell;
		vertical-align: top;
		padding: 0 5px;
	}
	.inline-form .form-group:last-child {
		vertical-align: bottom;
	}
}

@media screen and (min-width: 768px) {
	.programmes-form .form-body {
		display: table;
		width: 100%;
		padding-left: 10px;
		padding-right: 10px;
	}
	.programmes-form .form-group {
		width: 50%;
		float: left;
		padding: 0 5px;
	}
	.programmes-form .form-control {
		height: 40px;
		font-size: 16px;
		line-height: 26px;
	}
	.programmes-form .form-control.has-error {
		line-height: 24px;
	}
	.programmes-form-country {
		width: 30% !important;
	}
	.programmes-form-institution {
		width: 70% !important;
	}
	.programmes-form-qualification {
		width: 80% !important;
	}
	.programmes-form-submit {
		width: 20% !important;
		margin-top: 26px;
	}
}

.employer-form {
	*zoom: 1;
}

.employer-form:before,
.employer-form:after {
	content: '';
	display: table;
}

.employer-form:after {
	clear: both;
}

.employer-form .form-body {
	padding: 0;
}

.employer-form .form-body + .btn-more {
	margin-top: -1px;
}

.employer-form ul {
	list-style: none;
	padding: 0;
	margin: 0;
	border-bottom: 0;
}

.employer-form li {
	overflow: hidden;
	padding: 0;
	border-bottom: 1px solid #d1d3d4;
}

.employer-form li:last-child {
	border-bottom: 0;
}

.employer-form .form-header {
	display: block;
	margin: 0;
	padding: 10px 15px;
	font-size: 18px;
	font-family: 'Gotham bold', Arial, sans-serif;
	background: #eff0f0;
}

.employer-form .form-header span {
	white-space: nowrap;
	font-size: 14px;
	color: #666d70;
}

.employer-form .form-control {
	margin: 5px 15px;
	width: auto;
}

.employer-form .form-date {
	overflow: hidden;
}

.employer-form .form-date .form-control {
	float: left;
	max-width: 40px;
	margin-right: 5px;
	padding: 6px;
}

.employer-form .form-date .form-control + .form-control {
	margin-left: 0;
}

.employer-form .form-date .form-control:last-child {
	max-width: 60px;
}

.employer-form .form-group {
	float: left;
	width: 50%;
	margin: 0;
	padding: 0;
}

.employer-form .form-group:nth-child(odd) {
	clear: left;
}

.employer-form .form-pin {
	width: 40%;
}

.employer-form .form-date {
	width: 60%;
}

.employer-form .btn-more {
	clear: both;
	text-align: left;
}

.no-js .employer-form .btn-more {
	display: none;
}

.employer-form .btn-primary {
	margin-top: 10px;
}

@media screen and (max-width: 767px) {
	.employer-form .form-body {
		margin-left: -15px;
		margin-right: -15px;
		margin-bottom: 15px;
		border-left: 0;
		border-right: 0;
	}
	.employer-form .form-pin .form-control {
		max-width: 100px;
	}
}

@media screen and (min-width: 768px) {
	.employer-form .form-control {
		height: 40px;
		font-size: 16px;
		line-height: 26px;
	}
	.employer-form .form-control.has-error {
		line-height: 24px;
	}
	.employer-form .form-date .form-control {
		max-width: 50px;
	}
	.employer-form .form-date .form-control:last-child {
		max-width: 70px;
	}
	.employer-form .btn-primary {
		float: right;
		max-width: 150px;
	}
}

.logout-form {
	background: #438516;
}

.logout-form .btn-block,
.logout-form .form-group {
	width: auto;
	float: none;
}

.logout-form .form-group {
	overflow: hidden;
	padding: 5px;
	margin: 0;
}

.logout-form .form-group p {
	margin: 5px 10px;
	line-height: 20px;
	color: #ffffff;
}

.logout-form .btn-block {
	float: right;
	margin: 0;
	color: #31006f;
	background-color: #ffffff;
	min-width: 20%;
}

.logout-form .btn-block:hover {
	color: #d72882;
}

@media screen and (min-width: 768px) {
	.logout-form .form-group p {
		float: left;
		line-height: 30px;
	}
}

/**!
 * Module: Breadcrumb
 */

.breadcrumb-wrapper {
	overflow: hidden;
}

.breadcrumb-wrapper + h1 {
	margin-top: 0;
}

.breadcrumb {
	padding: 0;
	font-size: 18px;
	line-height: 20px;
	background: transparent;
}

.breadcrumb * {
	display: inline-block;
	vertical-align: top;
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
	height: 20px;
	font-size: 14px;
}

.breadcrumb span {
	font-family: 'Gotham book', Arial, sans-serif;
	color: #111111;
}

.breadcrumb ul {
	margin: 0;
	padding: 0;
}

.breadcrumb li + li:before {
	display: inline-block;
	content: '';
	width: 20px;
	height: 20px;
	background: url('/static/img/refresh/chevron.svg') center center no-repeat;
	background-size: auto 10px;
}

.breadcrumb svg,
.breadcrumb img {
	position: relative;
	top: -3px;
	width: 20px;
	height: 20px;
	fill: #31006f;
}

.breadcrumb a {
	color: #31006f;
}

.breadcrumb + h1 {
	margin-top: 0;
}

@media screen and (max-width: 767px) {
	.breadcrumb,
	.breadcrumb-wrapper {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
}

.lt-ie9 .breadcrumb {
	*zoom: 1;
}

.lt-ie9 .breadcrumb:before,
.lt-ie9 .breadcrumb:after {
	content: '';
	display: table;
}

.lt-ie9 .breadcrumb:after {
	clear: both;
}

.lt-ie9 .breadcrumb li {
	display: block;
	float: left;
}

/**!
 * Module: Carousel
 */

.carousel h3 {
	color: #005961;
}

.carousel h3.carousel-title {
	color: #fff;
}

.js .carousel .owl-carousel {
	cursor: pointer;
}

.carousel .owl-carousel {
	margin: 20px 0 30px 0;
}

.no-js .carousel .owl-carousel {
	display: block;
	padding: 0 !important;
}

.no-js .carousel .owl-carousel .carousel-item + .carousel-item {
	display: none;
}

.carousel .owl-buttons {
	position: absolute;
	right: 0;
	bottom: 0;
	width: 14.43299%;
}

.carousel .owl-prev,
.carousel .owl-next {
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-indent: 200%;
	float: left;
	width: 50%;
	height: 0;
	padding-bottom: 42.85714%;
	border: 1px solid #d1d3d4;
	background-color: #ffffff;
	background-position: center center;
	background-repeat: no-repeat;
	background-size: 21px 18px;
	background-image: url('/static/img/refresh/arrow-no-circle.svg');
}

.carousel .owl-prev {
	transform: rotate(180deg);
}

.lt-ie9 .carousel .owl-prev,
.no-svg .carousel .owl-prev {
	background-image: url('/static/img/carousel-prev.png');
}

.lt-ie9 .carousel .owl-next,
.no-svg .carousel .owl-next {
	background-image: url('/static/img/carousel-next.png');
}

.carousel .owl-page {
	display: inline-block;
	*display: inline;
	zoom: 1;
}

.carousel .owl-pagination {
	position: absolute;
	top: 20px;
	left: 30px;
	line-height: 12px;
}

.carousel .owl-page {
	margin-right: 8px;
}

.carousel .owl-page span {
	display: block;
	width: 12px;
	height: 12px;
	border-radius: 100%;
	border: 1px solid #ffffff;
}

.carousel .owl-page.active span {
	background: #ffffff;
}

.carousel-item {
	color: #ffffff;
	overflow: hidden;
	position: relative;
	background: #6571b3;
}

.carousel-image {
	width: 67%;
	float: right;
}

.carousel-title {
	line-height: 1;
	margin-bottom: 5px;
}

.carousel-overlay {
	position: absolute;
	float: left;
	width: 33%;
	height: 100%;
	background: #8917a6;
}

.carousel-overlay * {
	color: #ffffff;
}

.carousel-text {
	position: absolute;
	top: 60px;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
	padding: 0 30px;
	color: #ffffff;
}

.carousel-link {
	position: absolute;
	left: 30px;
	bottom: 20px;
	color: #ffffff;
}

.carousel-link svg {
	fill: #ffffff;
}

.carousel-link:hover {
	color: #ffffff;
	opacity: 0.8;
}

@media screen and (min-width: 768px) {
	.carousel .owl-next {
		border-left: 0;
	}
	.flexbox .carousel-text {
		top: 0;
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		-webkit-flex-direction: column;
		-ms-flex-direction: column;
		flex-direction: column;
		-webkit-justify-content: center;
		-ms-flex-pack: center;
		justify-content: center;
	}
}

@media screen and (min-width: 992px) {
	.carousel-title {
		margin-bottom: 10px;
	}
}

@media screen and (max-width: 767px) {
	.carousel .owl-carousel {
		padding-top: 50px;
	}
	.carousel .owl-controls {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 50px;
		background: #ffffff;
		border: 1px solid #d1d3d4;
	}
	.carousel .owl-pagination {
		position: relative;
		top: 0;
		left: 0;
		margin: 19px auto 0 auto;
		text-align: center;
	}
	.carousel .owl-page {
		margin: 0 4px;
	}
	.carousel .owl-page span {
		border-color: #b2b6b7;
	}
	.carousel .owl-buttons {
		position: static;
		width: auto;
	}
	.carousel .owl-prev,
	.carousel .owl-next {
		position: absolute;
		top: 0;
		width: 60px;
		height: 100%;
		padding: 0;
		border: 0;
	}
	.carousel .owl-prev {
		left: 0;
		border-right: 1px solid #d1d3d4;
	}
	.carousel .owl-next {
		right: 0;
		border-left: 1px solid #d1d3d4;
	}
	.carousel-image {
		width: 100%;
		float: none;
	}
	.carousel-overlay {
		position: relative;
		float: none;
		width: 100%;
		padding: 15px;
	}
	.carousel-text {
		position: relative;
		float: none;
		top: auto;
		left: auto;
		padding: 0;
		margin-bottom: 10px;
	}
	.carousel-link {
		position: relative;
		left: auto;
		bottom: auto;
	}
}

/**!
 * Module: Hub
 */

.hub {
	border-top: 1px solid #d1d3d4;
}

.hub .media {
	margin: 0;
}

.hub .media-body {
	padding: 20px 20px 20px 0;
}

@media screen and (max-width: 767px) {
	.hub .media-body {
		padding: 20px 10px;
	}
}

.hub .media-heading {
	margin-bottom: 16px;
	color: #31006f;
}

.hub .media-heading a {
	color: #31006f;
}
.hub .media-heading a:hover {
	color: #31006f !important;
}

.hub p {
	padding-left: 22px;
}

.hub .media-heading:before {
	vertical-align: baseline;
	display: inline-block;
	content: '';
	width: 20px;
	height: 20px;
	background: 0 center url('/static/img/refresh/chevron.svg') no-repeat;
	background-size: 19px 17px;
}

.hub .media-object {
	max-width: 220px;
	margin-right: 0;
}

@media screen and (max-width: 767px) {
	[class*='col-'] > .hub {
		margin-left: -15px;
		margin-right: -15px;
	}
}

@media screen and (max-width: 991px) {
	.hub .media-object {
		display: none !important;
		visibility: hidden;
	}
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.hub .media-body {
		padding-left: 0;
		padding-right: 0;
	}
}

@media screen and (min-width: 992px) {
	.hub {
		border: 0;
		margin: 0;
	}
	.hub .media {
		border-top: 1px solid #d1d3d4;
	}
	.js .hub .media {
		background-color: #ffffff;
		-webkit-transition: background-color 150ms ease;
		transition: background-color 150ms ease;
	}
}

/**!
 * Module: Latest News
 */

.news-icon {
	width: 91px;
	height: 91px;
}

.latest a {
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
	margin-bottom: 0;
	line-height: 1.35;
	font-size: 18px;
	text-decoration: underline;
}

.latest a:hover,
.latest a:focus {
	color: #d72882;
	text-decoration: underline;
}

.latest .media {
	margin: 20px 0;
}

.latest .media img:hover {
	cursor: pointer;
}

.latest .media-object {
	margin-right: 15px;
	max-width: 145px;
}

.latest .media-heading {
	font-size: 18px;
	color: #8917a6;
	display: block;
}

.latest .more-link {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.latest-title {
	margin-bottom: 20px;
}

.latest-title svg {
	display: inline-block;
	width: 20px;
	height: 20px;
}

@media screen and (max-width: 767px) {
	.latest .media {
		margin-left: -15px;
		margin-right: -15px;
		padding-left: 15px;
		padding-right: 15px;
		padding-bottom: 20px;
		border-bottom: 1px solid #d1d3d4;
	}
	.latest .media:last-child {
		padding-bottom: 0;
		border-bottom: 0;
	}
	.latest .media-object {
		display: none !important;
		visibility: hidden;
	}
}

/**!
 * Module: Learn More
 */

.learnmore h2,
.learnmore h3 {
	margin-bottom: 20px;
	color: #005961;
}

.learnmore ul {
	list-style: none;
	margin-bottom: 0;
	padding: 0;
}

.learnmore img {
	border-top: 8px solid #495ad4;
	margin-bottom: 10px;
}

.js .learnmore li:hover img {
	border-top: 8px solid #d72882;
}

.learnmore li {
	margin-bottom: 20px;
}

.learnmore li a {
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.learnmore li:last-child {
	border-right: 0 !important;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.learnmore li:nth-child(odd) {
		clear: left;
	}
	.learnmore li:nth-child(even) {
		float: right;
	}
}

@media screen and (min-width: 992px) {
	.learnmore li {
		border-right: 1px solid #b2b6b7;
	}
}

/**!
 * Module: News in Brief
 */

.news ul {
	list-style: none;
	padding: 0;
}

.news li {
	margin-bottom: 15px;
}

.news a {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.news-title {
	margin-bottom: 15px;
}

.news-title svg {
	display: inline-block;
	width: 20px;
	height: 20px;
}

.news-categories {
	padding: 0;
	background: none;
}

.news-categories .h4 {
	margin: 20px 15px;
}

.news-categories ul {
	list-style: none;
	display: block;
	padding: 0;
	margin: 0;
	color: #31006f;
	background: #ffffff;
}

.news-categories li {
	padding: 0;
	margin: 0;
	border-top: 1px solid #d1d3d4;
}

.news-categories li.is-last a,
.news-categories li:last-child a {
	font-family: 'Gotham bold', Arial, sans-serif;
	padding-left: 15px;
}

.news-categories li.is-last a:before,
.news-categories li:last-child a:before {
	display: none;
}

.news-categories a {
	display: block;
	padding: 10px 15px 10px 30px;
	font-size: 16px;
	line-height: 1.3;
	font-weight: normal;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.news-categories a:before {
	display: inline-block;
	vertical-align: top;
	content: '';
	width: 15px;
	height: 16px;
	background: url('/static/img/refresh/chevron.svg') 0 center no-repeat;
	background-size: auto 12px;
	margin-left: -15px;
}

.news-categories a span {
	color: #666d70;
}

.news-categories a:hover {
	color: inherit;
	text-decoration: underline;
}

.news-categories a:focus {
	color: inherit;
	text-decoration: underline;
}

/**!
 * Module: Notice
 */

.notice {
	padding: 15px;
	background: #fffbcc;
	border: none;
	border-left: 16px solid #feea00;
}

.notice hr {
	margin: 15px -15px;
	border-color: #d1d3d4;
}

.notice ul {
	padding-left: 15px;
}

.notice li {
	margin-bottom: 5px;
}

.notice li:last-child {
	margin-bottom: 0;
}

.notice ul,
.notice ol,
.notice p {
	font-size: 14px !important;
}

.notice ul:last-child,
.notice ol:last-child,
.notice p:last-child {
	margin-bottom: 0;
}

.notice-body {
	padding: 15px 15px;
	margin: -15px -15px;
	background: #fffbcc;
	border-bottom: 1px solid #d1d3d4;
}

.notice-body a {
	color: #111111;
	text-decoration: underline;
}

.notice-body a:hover,
.notice-body a:focus {
	color: #111111;
	text-decoration: none !important;
}

.notice-body + .notice-body {
	margin-top: 15px;
}

.notice-body:last-child {
	border-bottom: 0;
}

.notice-title {
	font-size: 24px;
	margin-bottom: 10px;
}

.notice-title svg,
.notice-title img {
	vertical-align: -2px;
	display: inline-block;
	fill: #111111;
	width: 20px;
	height: 20px;
}

.notice-title img {
	vertical-align: top;
	display: inline;
}

.notice-icon {
	display: block;
	float: left;
	width: 20px;
	height: 20px;
	margin-right: 10px;
	fill: #111111;
}

.notice-error {
	background: #fbebe5;
	border-color: #b44113;
}

.notice-error ul,
.notice-error ol,
.notice-error p {
	font-size: 18px !important;
}

/**!
 * Module: Paginated
 */

.pagination > .active > a {
	color: #111111;
	background-color: #d1d3d4;
}

.pagination > li > a {
	color: #31006f;
	border-radius: 0 !important;
	background-color: #eff0f0;
	border-color: #d1d3d4 !important;
	-webkit-transition: background-color 150ms ease;
	transition: background-color 150ms ease;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.pagination > li > a:hover {
	color: #d72882 !important;
	background-color: #ffffff !important;
}

.paginated-overflow {
	position: relative;
	overflow-y: hidden;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
}

.paginated-overflow .pagination {
	display: table;
}

.paginated-overflow .pagination li {
	display: table-cell;
}

.paginated-overflow .pagination li > a {
	display: block;
	float: none;
}

.paginated {
	*zoom: 1;
	background: #eff0f0;
	border: 1px solid #d1d3d4;
}

.paginated:before,
.paginated:after {
	content: '';
	display: table;
}

.paginated:after {
	clear: both;
}

.paginated p {
	margin: 0;
	padding: 0 15px;
	line-height: 45px;
}

.paginated .pagination {
	display: block;
	margin: 0;
}

.paginated .pagination > li > a {
	border-top: 0;
	border-bottom: 0;
}

@media screen and (max-width: 767px) {
	.paginated .pagination {
		width: 100%;
		border-top: 1px solid #d1d3d4;
	}
	.paginated .pagination > li > a:last-child,
	.paginated .pagination > li > a:first-child {
		border-left: 0;
	}
}

@media screen and (min-width: 768px) {
	.paginated p {
		float: left;
	}
	.paginated .pagination {
		float: right;
		margin: 0;
	}
	.paginated .pagination > li > a:last-child {
		border-right: 0;
	}
}

/**!
 * Module: Practitioner
 */

.practitioner {
	clear: both;
	overflow: hidden;
	margin: 20px 0;
}

.practitioner .media {
	clear: both;
	border-top: 0;
	margin-bottom: 20px;
}

.practitioner .table {
	margin: 0;
}

.practitioner .table th {
	color: #111111;
	background-color: #eff0f0;
}

.practitioner .table th:last-child {
	white-space: nowrap;
}

.practitioner .table + .table {
	border-top: 0;
}

.practitioner .table-responsive {
	margin: 15px 0 0 0;
}

.main-employer .practitioner {
	margin: 0;
}

.practitioner-meta {
	*zoom: 1;
}

.practitioner-meta:before,
.practitioner-meta:after {
	content: '';
	display: table;
}

.practitioner-meta:after {
	clear: both;
}

.practitioner-meta dl {
	display: block;
	font-size: 18px;
	padding: 15px;
	margin: 0;
	border: 1px solid #d1d3d4;
}

.practitioner-meta dl dt {
	font-family: 'Gotham bold', Arial, sans-serif;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.practitioner-status-positive {
	background-color: #ecf3e7;
}

.practitioner-status-positive .lead {
	color: #438516;
}

.practitioner-status-negative {
	background-color: #fbebe5;
}

.practitioner-status-caution {
	background-color: #fff3e5;
}

.practitioner-status {
	padding: 20px;
	border: 1px solid #d1d3d4;
}

.practitioner-status .media-object,
.practitioner-status .media-body {
	margin-top: 5px;
}

.practitioner-status .lead {
	margin-bottom: 0;
}

.practitioner-status + .practitioner-status {
	margin-top: -20px;
}

@media screen and (max-width: 767px) {
	.practitioner-meta dl:not(:last-of-type) {
		border-bottom: 0;
	}
}

@media screen and (min-width: 768px) {
	.practitioner-meta {
		display: table;
		width: 100%;
		border: 1px solid #d1d3d4;
	}
	.practitioner-meta dl {
		display: table-cell;
		width: 50%;
		border: 0;
		border-right: 1px solid #d1d3d4;
	}
	.practitioner-meta dl:last-child {
		border-right: 0;
	}
}

/**!
 * Module: Programme
 */

.programme {
	clear: both;
	overflow: hidden;
	margin: 20px 0;
}

.programme dl {
	display: block;
	font-size: 18px;
	padding: 15px;
	margin: 0;
	border: 1px solid #d1d3d4;
	word-wrap: break-word;
	word-break: break-word;
}

.programme dl:not(:last-of-type) {
	border-bottom: 0;
}

.programme dl dt {
	font-family: 'Gotham bold', Arial, sans-serif;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/**!
 * Module: Records
 */

.records dl {
	overflow: hidden;
	margin: 0;
	padding: 15px;
	border: 1px solid #d1d3d4;
}

.records dl:nth-of-type(even) {
	background-color: #eff0f0;
}

.records dl + dl {
	border-top: 0;
}

.records dt,
.records dd {
	display: block;
	float: left;
}

.records dt {
	clear: left;
	font-family: 'Gotham bold', Arial, sans-serif;
	margin-right: 5px;
}

.records dt:after {
	content: ':';
}

.records dd {
	margin-bottom: 5px;
}

.records dd:last-child {
	margin-bottom: 0;
}

.records-title {
	margin-bottom: 20px;
}

@media screen and (max-width: 767px) {
	.records dl {
		margin-left: -15px;
		margin-right: -15px;
		border-left: 0;
		border-right: 0;
	}
	.records .btn-more {
		margin-top: 15px;
	}
}

/**!
 * Module: Register
 */

.results {
	margin-bottom: 30px;
}

.results .more-link {
	font-size: inherit;
	color: #31006f;
}

.results .more-link svg {
	fill: #d72882;
}

.results .more-link span {
	display: inline;
}

.results .more-link:hover {
	color: #d72882;
}

.results .table {
	margin: 0;
	border: 1px solid #d1d3d4;
}

.results .table + .btn-more {
	margin-top: -1px;
}

.results .paginated {
	margin-top: 20px;
}

.results-col-institution {
	width: 25%;
}

.results-col-qualification {
	width: 55%;
}

.results-col-details {
	width: 20%;
}

.results-title {
	margin-bottom: 20px;
}

.results-negative {
	background-color: #fbebe5 !important;
}

.results-status {
	overflow: hidden;
}

.results-status svg {
	display: block;
	float: left;
	width: 20px;
	height: 20px;
}

.results-status svg img {
	width: 20px;
	height: 20px;
}

@media screen and (min-width: 768px) {
	.results td:last-child,
	.results td:first-child {
		font-family: 'Gotham bold', Arial, sans-serif;
	}
	.results th:last-child,
	.results td:last-child {
		text-align: right;
	}
	.results-status svg {
		width: 30px;
		height: 30px;
	}
	.results-status svg img {
		width: 30px;
		height: 30px;
	}
}

@media screen and (max-width: 767px) {
	.results .table {
		display: block;
		margin-left: -15px;
		margin-right: -15px;
		width: auto;
		max-width: none;
		border-left: 0;
		border-right: 0;
	}
	.results tbody {
		display: block;
	}
	.results tr {
		display: block;
		padding: 5px 15px;
		border-bottom: 1px solid #d1d3d4;
	}
	.results tr:last-child {
		border-bottom: 0;
	}
	.results td {
		position: relative;
		display: block;
		padding: 5px 0 5px 100px !important;
		border: 0 !important;
		line-height: 20px;
	}
	.results td:before {
		content: attr(data-label);
		position: absolute;
		display: block;
		top: 0;
		left: 0;
		padding: 5px 0;
		font-family: 'Gotham bold', Arial, sans-serif;
		line-height: 20px;
	}
	.results td:last-child a {
		color: #d72882;
		font-family: 'Gotham bold', Arial, sans-serif;
	}
}

/**!
 * Module: Searched
 */

.searched-header {
	overflow: hidden;
	padding: 15px 30px;
	border: 1px solid #d1d3d4;
	background-color: #ebfbf6;
}

.searched-header + .searched-list {
	border-top: 0;
}

.searched-meta {
	margin-bottom: 5px;
}

.searched-meta h1,
.searched-meta h2 {
	margin: 0;
}

.searched-meta * {
	vertical-align: top;
	display: inline-block;
	line-height: 35px;
}

.searched-meta-advanced svg {
	vertical-align: baseline;
	display: inline-block;
	width: 10px;
	height: 10px;
	fill: #d72882;
}

.searched-filters {
	overflow: hidden;
}

.searched-filters .form-group {
	display: block;
	margin: 0 0 10px 0;
}

.searched-filters .form-group:last-child {
	margin: 0;
}

.searched-list {
	margin: 0;
	padding: 0;
	list-style: none;
	border: 1px solid #d1d3d4;
}

.searched-list + .btn-more {
	margin-top: -1px;
}

.searched-item {
	border-bottom: 1px solid #d1d3d4;
	padding: 15px 30px;
	margin: 0;
}

.searched-item:last-child {
	border-bottom: 0;
}

.searched-link {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.searched-link a {
	display: inline-block;
	vertical-align: top;
	font-size: 14px;
	font-weight: normal;
}

@media screen and (min-width: 768px) {
	.searched-header {
		padding-top: 20px;
		padding-bottom: 20px;
	}
	.searched-meta {
		float: left;
		margin-bottom: 0;
	}
	.searched-meta-advanced {
		margin-left: 15px;
	}
	.searched-filters {
		float: right;
	}
	.searched-filters .form-group {
		float: left;
		margin: 0 10px 0 0;
	}
}

@media screen and (max-width: 767px) {
	.searched .btn-more {
		margin-top: 15px;
	}
	.searched-meta-advanced {
		display: block;
		margin: 0;
	}
	.searched-list,
	.searched-header {
		margin-left: -15px;
		margin-right: -15px;
		border-left: 0;
		border-right: 0;
	}
	.searched-item,
	.searched-header {
		padding: 15px;
	}
}

/**!
 * Module: Sections
 */

.sections > ul {
	margin: 20px 0;
	padding: 0;
	list-style: none;
	background: #eff0f0;
	border: 1px solid #d1d3d4;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 20px;
}

h1 + .sections > ul,
h2 + .sections > ul {
	margin-top: 10px;
}

.sections a {
	display: inline-block;
	padding: 6px 0;
}

.sections a:before {
	display: inline-block;
	content: '';
	width: 15px;
	height: 15px;
	background: url('/static/img/arrow-right--magenta.svg') 0 center no-repeat;
	background-size: auto 12px;
}

.sections li {
	border-bottom: 1px solid #d1d3d4;
}

.sections li:last-child {
	border-bottom: 0;
}

.sections > ul > li > a {
	display: block;
	background-color: #ffffff;
	padding-left: 20px;
}

.sections > ul > li > ul:first-child {
	border-top: 0;
	padding: 0 15px;
}

.sections ul ul {
	list-style: none;
	margin: 0;
	padding: 0 40px;
	border-top: 1px solid #d1d3d4;
}

.sections ul ul ul {
	font-size: 18px;
	padding: 0 0 0 20px;
	margin-bottom: 6px;
}

.sections ul ul ul a {
	padding-top: 3px;
	padding-bottom: 3px;
}

.sections ul ul ul ul {
	font-weight: normal;
}

.sections-title + ul {
	margin-top: 10px;
}

.sections-title a {
	padding: 0;
	color: #d72882;
}

.sections-title a:before {
	width: 20px;
	height: 20px;
	background-size: auto 20px;
	margin-right: 5px;
}

@media screen and (max-width: 767px) {
	.sections > ul {
		margin-left: -15px;
		margin-right: -15px;
		border-left: 0;
		border-right: 0;
	}
	.sections > ul ul {
		padding: 0 20px;
	}
}

/**!
 * Module: Share
 */

.heading-share {
	margin-bottom: 0;
}

.share .list-group {
	margin-bottom: 0;
}

.heading-share + .share {
	margin-top: 10px;
}

.share .list-group {
	border: none;
}

.share .list-group-item {
	padding: 10px 0 0;
	margin-right: 10px;
	margin-bottom: 10px;
	line-height: 20px;
	white-space: nowrap;
	border: none;
}

.share .list-group-item:last-child {
	color: #111111;
}

.share svg,
.share img {
	vertical-align: top;
	display: inline-block;
	width: 25px;
	height: 20px;
	margin-right: 5px;
	fill: #31006f;
}

.share .print-toolbox img {
	width: 30px;
	height: 27px;
}

.share .file {
	margin-right: 5px;
	margin-left: -2px;
}

.share .file-sprite {
	margin-right: 2px;
}

.share a {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.share a:hover {
	text-decoration: underline;
}

.share-small .list-group-item {
	padding: 5px 0;
	border-width: 0;
	border-bottom-width: 1px;
	height: auto !important;
}

.share-small .list-group-item:last-child {
	border-bottom: 0;
}

.share-small svg {
	vertical-align: middle;
	width: 15px;
	height: 15px;
}

.main-employer-results .share {
	margin-top: 15px;
}

.print-toolbox .list-group-item {
	border: none;
	padding: 0;
	margin-right: 20px;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 18px;
	margin-bottom: 20px;
}

@media screen and (min-width: 768px) {
	.share .list-group {
		display: table;
	}
	.share .list-group-item {
		display: table-cell;
		vertical-align: top;
	}
	.share .share-updated {
		width: 100%;
		text-align: right;
		border-left: 0;
	}
	.flexbox .share .list-group {
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
	}
	.flexbox .share .list-group-item {
		display: block;
		line-height: 20px;
		height: 42px;
	}
	.flexbox .share .share-updated {
		width: auto;
		-webkit-flex-grow: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		font-size: 14px;
		font-family: 'Gotham book', Arial, sans-serif;
	}
	.share-small .list-group-item {
		padding: 0 10px;
		border-bottom: 0;
	}
	.share-small .list-group-item:not(:last-child) {
		border-right: 1px solid #d1d3d4 !important;
	}
	.share-small .list-group-item:first-child {
		padding-left: 0;
	}
	.share-small .list-group-item:last-child {
		padding-right: 0;
	}
	.modal .heading-share {
		padding-top: 30px;
	}
	.modal .heading-share + .share {
		position: absolute;
		top: -30px;
		left: 15px;
		margin: 0;
	}
	.main-employer-results .share {
		margin-top: -31px;
		float: right;
	}
}

@media screen and (min-width: 992px) {
	.heading-share + .share {
		margin-top: -37px;
		float: right;
	}
}

/**!
 * Module: Signpost
 */

.signpost h2 {
	margin-bottom: 10px;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-weight: normal;
	color: #005961;
}

.signpost ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

.signpost li {
	border-bottom: 1px solid #74e0c1;
	padding: 0;
	font-size: 18px;
	line-height: 25px;
	font-family: 'Gotham bold', Arial, sans-serif;
	border-radius: 0;
}

.signpost li a {
	position: relative;
	display: block;
	padding: 15px;
	padding-right: 60px;
	color: #ffffff;
	background: #00749b;
	-webkit-transition: background-color 150ms ease;
	transition: background-color 150ms ease;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.signpost li a:hover {
	background: #015d7b;
	text-decoration: none;
}

.signpost li a:after {
	position: absolute;
	display: block;
	content: '';
	top: 0;
	right: 15px;
	width: 40px;
	height: 100%;
	background: url('/static/img/refresh/arrow-right-green.svg') center center no-repeat;
	background-size: 40px 40px;
}

@media screen and (min-width: 768px) {
	.signpost a {
		min-height: 80px;
	}
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.signpost ul {
		overflow: hidden;
		margin-left: -5px;
		margin-right: -5px;
	}
	.signpost li {
		float: left;
		width: 50%;
		padding-right: 5px;
	}
	.signpost li:nth-child(odd) {
		clear: left;
	}
	.signpost li:nth-child(even) {
		float: right;
	}
}

@media screen and (max-width: 767px) {
	.signpost h2 {
		padding: 10px 15px;
		margin-bottom: 5px;
	}
	.signpost ul,
	.signpost h2 {
		margin-left: -15px;
		margin-right: -15px;
	}
}

.lt-ie9 .signpost a {
	min-height: 50px;
}

/**!
 * Module: Social
 */

.btn-twitter,
.btn-facebook,
.btn-pinterest,
.btn-linkedin {
	width: 90px;
	padding: 10px 0;
}

.btn-twitter svg,
.btn-twitter img,
.btn-facebook svg,
.btn-facebook img,
.btn-pinterest svg,
.btn-pinterest img,
.btn-linkedin svg,
.btn-linkedin img {
	display: block;
	fill: #ffffff;
	margin: 0 auto;
	height: 25px;
}

.btn-twitter {
	background: #2aa7de !important;
}

.btn-twitter svg,
.btn-twitter img {
	width: 31px;
}

.btn-facebook {
	background: #3e5b97 !important;
}

.btn-facebook svg,
.btn-facebook img {
	width: 13px;
}

.btn-pinterest {
	background: #c3282d !important;
}

.btn-pinterest svg,
.btn-pinterest img {
	width: 22px;
}

.btn-linkedin {
	background: #4393cc !important;
}

.btn-linkedin svg,
.btn-linkedin img {
	width: 31px;
}

.social {
	*zoom: 1;
}

.social:before,
.social:after {
	content: '';
	display: table;
}

.social:after {
	clear: both;
}

.social .btn {
	margin-right: 5px;
	margin-bottom: 5px;
}

@media screen and (min-width: 992px) {
	.social.aside .btn {
		display: block;
		float: left;
		max-width: 100px;
		width: 48%;
		margin-right: 2%;
		margin-bottom: 5px;
		width: calc(50% - 5px);
		margin-right: calc(1px + 4px);
	}
	.social.aside .btn:nth-child(odd) {
		margin-right: 0;
	}
}

/**!
 * Module: Stats
 */

.stats ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.stats li {
	margin-bottom: 15px;
	overflow: hidden;
}

.stats-title {
	font-size: 24px;
	line-height: 25px;
	margin-bottom: 15px;
}

.stats-header {
	overflow: hidden;
	margin: -16px !important;
	margin-bottom: 15px !important;
	padding: 15px 16px;
	color: #ffffff;
	background: #00749b;
}

.stats-header svg {
	fill: #74e0c1;
	width: 100px;
	height: 100px;
	margin-top: -30px;
	margin-bottom: 2px;
}

.stats-header span {
	display: block;
}

.stats-number {
	display: block;
	color: #00749b;
	font-size: 40px;
	line-height: 1;
	margin-bottom: 5px;
}

.stats-group {
	position: relative;
	padding-bottom: 30px;
	margin-bottom: 30px;
}

.stats-group:after {
	position: absolute;
	display: block;
	content: '';
	width: 100%;
	height: 4px;
	top: 100%;
	margin-top: -2px;
	background: #d1d3d4;
}

.stats-group:last-child {
	padding-bottom: 0;
	margin-bottom: 0;
}

.stats-group:last-child:after {
	display: none;
}

.stats .media-object {
	max-width: 80px;
}

@media screen and (max-width: 767px) {
}

@media screen and (min-width: 768px) {
	.module.stats {
		display: table;
		width: 100%;
		height: 100%;
		padding: 30px 0;
		border: 2px solid #d1d3d4;
	}
	.module.stats .media-object {
		float: none !important;
		margin-bottom: 10px;
	}
	.module.stats .stats-group {
		display: table-cell;
		width: 33.33%;
		height: 100%;
		margin: 0;
		padding: 0 30px;
		border-right: 4px solid #d1d3d4;
	}
	.module.stats .stats-group:last-child {
		border-right: 0;
	}
	.module.stats .stats-group:after {
		display: none;
	}
}

/**!
 * Module: Subnav
 */

.subnav {
	background: #ffffff;
	background: rgba(255, 255, 255, 0.8);
	margin-bottom: 15px;
}

.subnav h2 {
	display: block;
	margin: 0;
	font-size: 24px;
	line-height: 22px;
	color: #005961;
	padding: 32px 15px 32px 0;
	margin-bottom: 0px;
	background: #fff;
	border-top: 1px solid #005961;
}

.main-home .subnav h2 {
	background-color: #ebfbf6;
	padding: 32px 15px;
	border-top: none;
}

.subnav ul {
	list-style: none;
	display: block;
	padding: 0;
	margin: 0;
	color: #31006f;
}

.subnav li {
	padding: 0;
	margin: 0;
	border-top: 1px solid #005961;
}

.subnav a {
	display: block;
	padding: 18px 15px 18px 30px;
	font-size: 16px;
	line-height: 1.3;
	font-family: 'Gotham bold', Arial, sans-serif;
	background: #ebfbf6;
	color: #111111;
}

.subnav a:before {
	display: inline-block;
	vertical-align: top;
	content: '';
	width: 15px;
	height: 16px;
	background: url('/static/img/refresh/chevron.svg') center 4px no-repeat;
	background-size: auto 12px;
	margin-left: -15px;
}

.subnav a.is-current {
	background-color: #74e0c1 !important;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-decoration: underline;
}

.subnav a:focus,
.subnav a:hover {
	color: inherit;
	text-decoration: none;
	color: #111111;
}

.subnav a:hover {
	background-color: rgba(116, 224, 193, 0.3);
}

.subnav ul ul a {
	padding-left: 45px;
	font-weight: normal;
}

.subnav ul ul ul a {
	padding-left: 60px;
}

/**!
 * Navigation
 */

/*!
 * dotcentric-nav (NMC theme)
 * http://www.dotcentric.co.uk
 * Copyright (c) dotcentric 2014
 */

.dcnav-main {
	background: #fff;
}

.dcnav {
	z-index: 5000;
	width: 100%;
	color: #ffffff;
}

.dcnav-wrapper {
	background: #495ad4;
}

.dcnav-link,
.dcnav-back,
.dcnav-close {
	display: block;
	-webkit-appearance: none;
	-moz-appearance: none;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	outline: none;
	background: #e5e5e1;
	border: none;
	border-radius: 0;
	padding: 15px;
	color: #3d85cc;
	font-size: 16px;
	line-height: 20px;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-align: left;
	text-decoration: none;
}

.dcnav-link:hover,
.dcnav-back:hover,
.dcnav-close:hover {
	text-decoration: none;
}
/* 
.dcnav-link:after,
.dcnav-back:after,
.dcnav-close:after {
	position: absolute;
	display: block;
	content: '';
	top: 0;
	left: 0;
	width: 30px;
	height: 44px;
	background-repeat: no-repeat;
	background-position: center center;
	pointer-events: none;
} */

.dcnav-link {
	color: #ffffff;
	padding: 12px 20px;
	background: #495ad4;
	font-family: 'Gotham bold', Arial, sans-serif;
}

@media screen and (min-width: 992px) {
	.dcnav-item .dcnav-menu .dcnav-link {
		color: #495ad4;
		background: transparent;
	}
}

.dcnav-link:hover {
	color: #ffffff;
	background: #00838f;
	text-decoration: none;
}

.dcnav-link:not([href]) {
	color: #ffffff;
}

.dcnav-back {
	background-color: #31006f !important;
}

.dcnav-back:after {
	left: 4px !important;
	right: auto !important;
	background-image: url('/static/img/refresh/chevron-white.svg') !important;
	transform: rotate(180deg);
	content: '';
	display: block;
	position: absolute;
	display: block;
	content: '';
	top: 0;
	width: 30px;
	height: 44px;
	background-repeat: no-repeat;
	background-position: center center;
	pointer-events: none;
}

.dcnav-close {
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-indent: 200%;
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 55px;
	height: 54px;
	color: #ffffff;
	background: transparent;
	padding: 0;
	outline: 0;
	border: 0;
}

.dcnav-close:after {
	display: none !important;
	background: none !important;
}

.dcnav-close span {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}

.dcnav-close svg {
	position: absolute;
	top: 0;
	left: 0;
	width: 22px;
	height: 22px;
	margin: 16px;
	fill: #ffffff;
	-webkit-transition: fill 100ms ease;
	transition: fill 100ms ease;
}

.dcnav-close:hover {
	color: #00838f;
	background: transparent;
	text-decoration: none;
}

.dcnav-close:hover svg {
	fill: #ffffff;
}

.no-svg .dcnav-close {
	background: url('../img/close.png') center center no-repeat;
}

.dcnav-title > .dcnav-link:not([href]) {
	color: #797c80;
	cursor: default;
}

.dcnav-title img {
	display: none;
}

.dcnav-feature {
	position: relative;
	border-top: 0 !important;
	border-bottom: 0 !important;
	background: #31006f;
}

@media screen and (min-width: 768px) {
	.dcnav-feature {
		float: left;
		clear: none !important;
		width: 50%;
	}

	.dcnav-feature:nth-of-type(1) {
		width: 52%;
		border-right: 1px solid rgba(255, 255, 255, 0.3);
	}

	.dcnav-feature:nth-of-type(2) {
		width: 48%;
	}
}

.dcnav-feature svg {
	float: left;
	width: 26px;
	height: 30px;
	margin-right: 10px;
	fill: #ffffff;
}

.dcnav-feature a {
	display: block;
	height: 70px;
	padding: 15px 0 0 15px;
	color: #ffffff;
}
@media screen and (max-width: 767px) {
	.dcnav-feature a {
		padding: 15px 44px 15px 15px;
		height: auto;
	}
	.dcnav-feature a .icon {
		position: absolute;
		right: 10px;
	}
}
.dcnav-feature a:hover {
	text-decoration: none;
}

@media screen and (min-width: 768px) {
	.dcnav-feature {
		display: none !important;
		visibility: hidden;
	}
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.dcnav-feature + .dcnav-item {
		border-top: 1px solid rgba(0, 183, 198, 0.3) !important;
	}
}

.dcnav-listen {
	position: absolute;
	display: block;
	top: 15px;
	right: 15px;
	font-size: 14px;
	line-height: 20px;
	color: #ffffff;
	fill: #ffffff;
}

.dcnav-listen:focus,
.dcnav-listen:hover {
	color: #ffffff;
	text-decoration: none;
}

.dcnav-listen svg,
.dcnav-listen img {
	display: inline-block;
	vertical-align: -2px;
	width: 18px;
	height: 15px;
}

@media screen and (min-width: 768px) {
	.dcnav-listen {
		display: none !important;
		visibility: hidden;
	}
}

@media screen and (max-width: 991px) {
	.dcnav-root {
		-webkit-transition-duration: 400ms;
		transition-duration: 400ms;
	}
	.dcnav {
		background: #495ad4;
		-webkit-transition-duration: 400ms;
		transition-duration: 400ms;
	}
	.dcnav.is-effect-push:after,
	.dcnav.is-effect-behind:after {
		pointer-events: none;
		position: absolute;
		display: block;
		content: '';
		top: 0;
		right: 0;
		bottom: 0;
		width: 15px;
		height: 100%;
		background: -webkit-linear-gradient(left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.1));
		background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.1));
	}
	.dcnav-wrapper {
		top: 160px;
		width: 100% !important;
		min-width: 0 !important;
	}
	.dcnav-link {
		width: 100%;
	}
	.dcnav-link:hover {
		background: #31006f;
	}
	.dcnav-back {
		border-bottom: 1px solid rgba(0, 183, 198, 0.3);
	}
	.dcnav-item {
		border-bottom: 1px solid rgba(255, 255, 255, 0.3);
	}
	.dcnav-item:first-child {
		border-top: 1px solid rgba(0, 183, 198, 0.3);
	}
	.dcnav-item.is-parent > .dcnav-link {
		padding-right: 30px;
	}
	.dcnav-item.is-parent > .dcnav-link:after {
		right: 9px;
		left: auto;
		background-image: url('/static/img/refresh/chevron-white.svg');
		content: '';
		display: block;
		position: absolute;
		display: block;
		content: '';
		top: 0;
		width: 30px;
		height: 44px;
		background-repeat: no-repeat;
		background-position: center center;
		pointer-events: none;
	}
	.dcnav-item .dcnav-menu .dcnav-link {
		padding-left: 30px;
	}
	.dcnav-item .dcnav-menu .dcnav-title .dcnav-link {
		padding-left: 15px;
	}
	.no-js .dcnav {
		display: none !important;
		visibility: hidden;
	}

	.dcnav-menu .header-search {
		display: none;
	}
}

/*!
 * dotcentric-nav (desktop)
 */

@media screen and (min-width: 992px) {
	.dcnav {
		position: relative;
		z-index: 999;
	}
	.dcnav-item {
		border: 0 !important;
	}
	.dcnav-item .dcnav-item:hover {
		background: transparent !important;
	}
	.dcnav-wrapper {
		padding: 0 15px;
	}
	.dcnav-wrapper > .dcnav-menu {
		max-width: 970px;
		margin: 0 auto;
	}
	.flexbox .dcnav-wrapper > .dcnav-menu {
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		-webkit-flex-wrap: nowrap;
		-ms-flex-wrap: nowrap;
		flex-wrap: nowrap;
		padding-right: 50px;
	}
	.dcnav-wrapper > .dcnav-menu > .dcnav-item {
		border-right: 1px solid rgba(255, 255, 255, 0.3) !important;
	}
	.flexbox .dcnav-wrapper > .dcnav-menu > .dcnav-item {
		-webkit-flex: auto;
		-ms-flex: auto;
		flex: auto;
	}
	.flexbox .dcnav-wrapper > .dcnav-menu > .dcnav-item > .dcnav-link {
		text-align: center;
		white-space: nowrap;
	}
	/* .dcnav-wrapper > .dcnav-menu > .dcnav-item.is-current {
		background-color: #00838f;
	} */
	.dcnav-wrapper > .dcnav-menu > .dcnav-item.is-current:hover {
		background-color: #eff0f0;
	}

	.dcnav-wrapper > .dcnav-menu > .dcnav-item.is-current:hover button.drop {
		background-image: url('/static/img/refresh/chevron.svg');
		transform: rotate(270deg);
	}

	.dcnav-menu:before,
	.dcnav-menu:after {
		content: '';
		display: table;
	}
	.dcnav-menu:after {
		clear: both;
	}
	.dcnav-link {
		height: 40px;
		padding: 10px 15px;
		background: transparent;
		transition: none;
	}
	.dcnav-link:hover {
		background: transparent;
	}
	.dcnav-item {
		position: relative;
		display: block;
		float: left;
		border-bottom: 0;
		background: transparent;
	}
	.dcnav-item .dcnav-item {
		position: static;
		float: none;
	}
	.dcnav-item .dcnav-item .dcnav-link {
		height: auto;
	}
	.dcnav-item .dcnav-item .dcnav-link:hover,
	.dcnav-item .dcnav-item .dcnav-link:focus {
		text-decoration: underline;
		color: #d72882;
	}
	.dcnav-item .dcnav-item .dcnav-link:focus {
		outline-offset: -3px !important;
	}
	.dcnav-item .dcnav-item .dcnav-link:after {
		display: none;
	}
	.dcnav-item .dcnav-item .dcnav-link:before {
		position: absolute;
		display: inline-block;
		vertical-align: middle;
		height: 10px;
		width: 15px;
		background-image: url('/static/img/refresh/chevron.svg');
		background-position: left center;
		background-size: auto 100%;
		content: '';
		top: 15px;
		left: 15px;
		background-repeat: no-repeat;
		pointer-events: none;
	}
	.dcnav-item .dcnav-menu {
		position: absolute;
		top: 100%;
		left: 0;
		min-width: 200px;
		padding: 20px 0;
		background: #eff0f0;
		border: 1px solid #d1d3d4;
		border-top: 0;
	}
	.dcnav-item .dcnav-menu .dcnav-menu {
		display: none !important;
	}
	.dcnav-item:hover,
	.dcnav-item .dcnav-link.active {
		background: #eff0f0;
	}
	.dcnav-item:hover .dcnav-link,
	.dcnav-item .dcnav-link.active,
	.dcnav-link.active + .dcnav-menu .dcnav-link {
		color: #31006f;
	}
	.dcnav-item:hover > .dcnav-menu,
	.dcnav-link.active + .dcnav-menu {
		display: block;
	}
	.csscolumns .dcnav-item.is-mega {
		position: static;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child {
		display: block !important;
		position: absolute;
		overflow: hidden;
		top: 20px;
		left: 30px;
		width: calc(30% - 45px);
		height: calc(100% - 40px);
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title {
		display: block !important;
		position: absolute;
		width: 100%;
		max-height: 100%;
		padding-bottom: 40px;
		color: #ffffff;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title img {
		display: block;
		border: 3px solid #d72882;
		border-bottom: 0;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title .dcnav-link {
		position: absolute;
		bottom: 0;
		width: 100%;
		color: #ffffff;
		background: #d72882;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title .dcnav-link:before {
		background-image: url(/static/img/refresh/chevron-white.svg);
	}

	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title .dcnav-link:focus {
		outline-color: #31006f !important;
		outline-offset: -3px !important;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title .dcnav-link:hover {
		color: #ffffff;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child .dcnav-title .dcnav-link:after {
		position: static;
		display: inline-block;
		vertical-align: middle;
		height: 10px;
		width: 15px;
		background-image: url('/static/img/arrow-right.svg');
		background-position: right center;
		background-size: auto 100%;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu {
		-webkit-column-count: 3;
		-moz-column-count: 3;
		column-count: 3;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item {
		border-right: 0 !important;
	}
	.csscolumns .dcnav-item.is-mega > .dcnav-menu > .dcnav-item:first-child ~ .dcnav-item {
		display: block;
	}
	.csscolumns .dcnav-item.is-mega .dcnav-menu {
		padding-left: 30%;
		width: 100%;
		min-height: 240px;
	}
}

/**!
 * Global Elements
 */

.header {
	z-index: 1000;
	position: absolute;
	overflow: hidden;
	top: 0;
	left: 0;
	width: 100%;
}

.header .container {
	background: #ffffff;
}

.header .row {
	position: relative;
	padding: 0 15px;
	height: 140px;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.header .row {
		height: 160px;
	}
}

@media screen and (max-width: 767px) {
	.header {
		position: fixed;
		border-bottom: 1px solid #d1d3d4;
	}
	.header .row {
		height: 55px;
	}
	.iOS.js-focus .header {
		position: absolute;
	}
}

@media screen and (min-width: 992px) {
	.header {
		overflow: visible;
	}
}

.header-notice {
	z-index: 101;
	position: relative;
}

.header-notice .notice {
	padding-left: 0;
	padding-right: 0;
	border-left: 0;
	border-right: 0;
}

.header-notice .prose {
	overflow: hidden;
	margin-bottom: 0;
}

.header-notice .notice .btn {
	background: transparent;
}

@media screen and (max-width: 767px) {
	.header-notice {
		top: 160px;
	}
	.header-notice [class*='col-']:not(:last-child) {
		margin-bottom: 15px;
	}
}

@media screen and (max-width: 767px) {
	.header-notice {
		top: 55px;
	}
}

.logo {
	width: 302px;
	height: 67px;
	margin: 30px 0;
}

.logo a {
	display: block;
	width: 100%;
	height: 100%;
}

.logo a:focus {
	outline-offset: 1px !important;
}

.logo {
	background-image: url('https://www.nmc.org.uk/static/img/refresh/logo-color-side.svg');
	background-position: 0 0;
	background-repeat: no-repeat;
	background-size: 302px 67px;
}

.logo.logo--form {
	background-image: url('https://www.nmc.org.uk/static/img/refresh/logo-white-on-top.svg');
	background-size: 140px 120px;
	width: 140px;
	height: 120px;
}

@media screen and (max-width: 1199px) {
	.logo.logo--form {
		margin: 20px 0;
		height: 83px;
		background-size: contain;
	}
}

.logo img {
	display: none;
}

.icon-24 {
	width: 24px;
	height: 24px;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.logo {
		width: 176px;
		height: 70px;
		margin: 25px 0;
		background-size: 173px 71px;
	}
}

@media screen and (max-width: 767px) {
	.logo {
		width: 164px;
		height: 40px;
		margin: 10px 0 0 52px;
		background-size: 157px 35px;
	}
}

.header-links {
	display: table;
	position: absolute;
	top: 0;
	right: 15px;
	padding: 0;
	margin: 0;
	list-style: none;
	border-top: 0;
}

.header-links li {
	display: table-cell;
	*display: inline;
}

.header-links li:last-child {
	border-right: 0;
}

.header-links a {
	display: block;
	*display: inline;
	font-size: 14px;
	line-height: 20px;
	padding: 5px 10px;
	white-space: nowrap;
	color: #31006f;
	text-decoration: underline;
}

.header-links a:hover,
.header-links a:focus {
	text-decoration: none;
}
@media screen and (max-width: 767px) {
	.header-links {
		display: none !important;
		visibility: hidden;
	}
}

.header-feature {
	position: absolute;
	top: 60px;
	right: 15px;
}

.header-feature a {
	position: relative;
	display: block;
	float: left;
	color: #31006f;
	padding-right: 35px;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 18px;
}

.header-feature a span:not(.icon) {
	color: #31006f;
}

.header-feature a:hover {
	text-decoration: underline;
}

.header-feature a:hover .header-title {
	color: #31006f;
	text-decoration: underline;
}

.header-feature .icon {
	position: absolute;
	top: 50%;
	right: 0;
	width: 24px;
	height: 24px;
	fill: #8917a6;
	transform: translateY(-50%);
}

.header-feature img {
	width: auto;
}

.header-feature .header-title {
	margin: 0;
	font-size: 16px;
	font-family: 'Gotham bold', Arial, sans-serif;
	line-height: 22px;
	color: #d72882;
	white-space: nowrap;
	text-decoration: underline;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.header-feature {
		top: 50px;
	}
}

@media screen and (max-width: 767px) {
	.header-feature {
		display: none !important;
		visibility: hidden;
	}
}

.header-register {
	margin-left: 36px;
}

.header-bar {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
}

.header-nav,
.header-search {
	position: absolute;
	display: block;
	width: 55px;
	height: 54px;
	color: #ffffff;
}

.header-nav svg,
.header-search svg,
.header-search img {
	position: absolute;
	top: 0;
	left: 0;
	width: 24px;
	height: 22px;
	margin: 16px 14px;
	fill: #31006f;
	-webkit-transition: fill 100ms ease;
	transition: fill 100ms ease;
	pointer-events: none;
}

.header-nav:hover,
.header-search:hover {
	color: #ffffff;
	text-decoration: none;
}

.header-nav {
	left: 0;
}

.no-svg .header-nav {
	background: url('../img/nav.png') center center no-repeat;
}

.header-search {
	right: 0;
}

.no-svg .header-search {
	background: url('../img/search.png') center center no-repeat;
}

.header-bar .header-search {
	display: none;
}

@media screen and (max-width: 767px) {
	.header-nav span,
	.header-search span {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
	.header-nav {
		border-right: 1px solid #d1d3d4;
	}
	.header-search {
		border-left: 1px solid #d1d3d4;
	}
	.header-bar .header-search {
		display: block;
	}
}

@media screen and (max-width: 767px) {
	.header {
		-webkit-transition: left 400ms ease;
		transition: left 400ms ease;
	}
	.dcnav-root.is-nav .header {
		left: 260px;
	}
	.dcnav-wrapper {
		top: 55px;
	}
	.dcnav-wrapper > .dcnav-menu {
		border-top: 0;
	}
}

.header-search .header-search__img {
	display: none;
}
.header-search .header-search__img--active {
	display: block;
}

@media screen and (min-width: 768px) {
	.header {
		position: relative;
	}
	.header-bar {
		background: #495ad4;
		top: auto;
		bottom: 0;
		left: -15px;
		right: -15px;
		width: auto;
		height: 40px;
	}
	.header-nav,
	.header-search {
		height: 40px;
		font-size: 18px;
		line-height: 40px;
	}
	.header-nav svg,
	.header-search svg,
	.header-search img {
		margin: 8px;
		fill: #ffffff;
	}
	.header-nav {
		left: 22px;
		width: auto;
		padding-left: 40px;
	}
	.header-nav:hover,
	.header-nav:focus {
		color: #fff;
	}
	.header-nav:hover svg {
		fill: #fff;
	}
	.header-search {
		right: 30px;
		width: 50px;
	}
	.header-search svg,
	.header-search img {
		left: 5px;
	}
	.header-search:focus,
	.header-search.is-active {
		background: #eff0f0;
	}
	.header-search .header-search__img {
		display: block;
	}
	.header-search .header-search__img--active {
		display: none;
	}
	.header-search.is-active .header-search__img,
	.header-search:focus .header-search__img {
		display: none;
	}
	.header-search.is-active .header-search__img--active,
	.header-search:focus .header-search__img--active {
		display: block;
	}

	.header-search:focus svg,
	.header-search.is-active svg {
		fill: #31006f;
	}
	.header-search span {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
}

@media screen and (min-width: 992px) {
	.header {
		position: relative;
		height: auto;
		min-height: 140px;
	}
	.header .container {
		height: auto;
		min-height: 140px;
	}
	.lt-ie9 .header .container {
		height: 140px;
	}
	.header-bar {
		height: 0;
		top: 100%;
		left: 15px;
		right: 15px;
		bottom: auto;
		background: transparent;
	}
	.header-search {
		right: 0;
	}
	.header-nav {
		display: none !important;
		visibility: hidden;
	}
}

.footer {
	position: relative;
	color: #ffffff;
	z-index: 100;
}

.footer a {
	color: #ffffff;
}

.footer .container {
	background: #495ad4;
}

.footer-text {
	padding: 50px 0 12px;
}

.footer-text__item {
	font-size: 38px;
	font-family: 'Gotham bold', Arial, sans-serif;
	display: block;
	color: #ffffff;
	line-height: 1.3;
}

/* .footer-text__item--1 {
	color: #327fef;
}

.footer-text__item--2 {
	color: #db3c8e;
}

.footer-text__item--3 {
	color: #74e0c1;
} */

.footer-prose {
	font-size: 18px;
	line-height: 22px;
	padding: 0 0 20px 0;
}

.footer-prose p:last-child {
	margin-bottom: 0;
}

.footer-social,
.footer-primary,
.footer-secondary {
	list-style: none;
	padding: 0;
	margin: 0;
}

.footer-primary {
	font-size: 18px;
	line-height: 20px;
	margin-bottom: 30px;
}

.footer-primary li {
	position: relative;
	display: block;
}

.footer-primary img {
	position: absolute;
	width: 15px;
	height: 15px;
	fill: #ffffff;
}

.footer-primary a {
	display: block;
	padding: 10px;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-decoration: underline;
}

.footer-primary a:hover,
.footer-primary a:focus {
	text-decoration: none;
}

.footer-secondary {
	font-size: 16px;
	line-height: 20px;
	margin-bottom: 20px;
}

.footer-secondary li {
	margin-bottom: 10px;
}

.footer-social {
	display: table;
	clear: left;
	margin-bottom: 20px;
}

.footer-social li {
	display: table-cell;
}

.footer-twitter,
.footer-facebook {
	display: block;
	height: 40px;
	background-position: 0 0;
	background-repeat: no-repeat;
	margin-right: 20px;
}

.footer-twitter svg,
.footer-facebook svg {
	width: 100%;
	height: 40px;
	fill: #ffffff;
}

.footer-twitter {
	width: 50px;
}

.footer-facebook {
	width: 20px;
}

.footer-small {
	clear: both;
	margin-bottom: 20px;
	font-family: 'Gotham bold', Arial, sans-serif;
}

@media screen and (max-width: 991px) {
	.footer-primary {
		margin-left: -30px;
		margin-right: -30px;
		border-top: 1px solid rgba(0, 183, 198, 0.3);
		line-height: 30px;
	}
	.footer-primary li {
		border-bottom: 1px solid rgba(0, 183, 198, 0.3);
	}
	.footer-primary img {
		top: 17px;
		right: 20px;
	}
	.footer-primary a {
		padding: 10px 40px 10px 30px;
	}
}

@media screen and (max-width: 767px) {
	.footer-primary {
		margin-left: -15px;
		margin-right: -15px;
	}
	.footer-primary img {
		right: 10px;
	}
	.footer-primary a {
		padding-left: 15px;
	}
}

@media screen and (min-width: 768px) {
	.footer-prose {
		font-size: 24px;
		line-height: 30px;
		padding: 0px 0 20px;
	}
	.footer-social {
		float: left;
		margin-bottom: 30px;
	}
	.footer-secondary {
		clear: both;
		width: 100%;
		font-size: 14px;
		line-height: 25px;
	}
	.footer-secondary li {
		display: inline-block;
		margin-right: 30px;
		margin-bottom: 0;
	}
}

@media screen and (min-width: 992px) {
	.footer-prose {
		padding: 60px 0 20px;
	}

	.footer-text {
		float: left;
		width: 25%;
		padding-right: 20px;
	}
	.footer-prose {
		float: left;
		width: 35%;
	}
	.footer-primary {
		float: right;
		width: 40%;
		margin: 0;
		padding: 55px 0 20px;
		font-size: 16px;
		line-height: 20px;
	}
	.footer-primary li {
		float: left;
		width: 42%;
		padding-left: 8%;
	}
	.footer-primary img {
		top: 12px;
		fill: #d72882;
	}
	.footer-primary a {
		padding-left: 20px;
	}
}

.search .container {
	padding: 0 15px;
}

.search .form-group {
	position: relative;
	margin: 0 auto;
	padding: 20px;
	padding-right: 100px;
	max-width: 970px;
	background: #eff0f0;
	border: 1px solid #d1d3d4;
}

.search-icon {
	display: none;
	fill: #31006f;
}

.search-criteria:focus + .search-icon {
	fill: #d72882;
}

.search-criteria:valid {
	background-color: #fbfccc;
}

.search-submit {
	position: absolute;
	top: 20px;
	right: 20px;
	width: 100px;
	line-height: 22px;
}

@media screen and (min-width: 768px) {
	.search .container {
		padding: 0 30px;
	}
	.search .form-group {
		padding-right: 140px;
	}
	.search-icon {
		display: block;
		position: absolute;
		top: 32px;
		left: 32px;
		width: 20px;
		height: 20px;
	}
	.search-criteria {
		padding-left: 40px;
		height: 45px;
		font-size: 18px;
		line-height: 33px;
	}
	.search-submit {
		width: 120px;
		height: 45px;
		font-size: 18px;
		line-height: 33px;
	}
}

.js .is-nav .search,
.js .is-nav-active .search {
	display: none !important;
}

.js .search {
	z-index: 200;
	position: absolute;
	width: 100%;
	top: -999px;
	display: none;
}

.js .search.is-active {
	top: 0;
	display: block;
}

.js .search .form-group {
	border-top: 0;
}

@media screen and (min-width: 768px) and (max-width: 991px) {
	.js .search.is-active {
		top: 0px;
	}
	.header-bar .header-search {
		display: block;
	}
}

@media screen and (max-width: 767px) {
	.js .search {
		position: fixed;
	}
	.js .search.is-active {
		top: 55px;
	}
	.js .search .container {
		padding: 0;
	}
	.js .search .form-group {
		border-left: 0;
		border-right: 0;
	}
	.js.iOS .search {
		position: absolute;
	}
}

.no-js .search > .container {
	padding: 30px;
	background: #eff0f0;
}

@media screen and (max-width: 767px) {
	.no-js .search > .container {
		padding: 15px;
	}
}

.banner {
	position: relative;
	width: 100%;
	margin: 0 auto;
	max-width: 1500px;
	background: #ffffff;
}

.banner.banner-large {
	background: #31006f;
}

.banner-container {
	position: absolute;
	top: 60%;
	left: 0;
	right: 0;
	bottom: 0;
	background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.7));
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 30%, rgba(0, 0, 0, 0.7));
}

.no-cssgradients .banner-container {
	background: transparent url('/static/img/banner-fade.png') left bottom repeat-x;
	background-size: 100% 100%;
}

.banner-large .banner-container {
	top: 40%;
}

.banner-content {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	max-width: 1030px;
	margin: 0 auto;
	padding: 15px;
	padding-top: 0;
}

.banner-title {
	color: #ffffff;
	text-shadow: 1px 1px 4px black;
	filter: progid: DXImageTransform.Microsoft.Shadow(color=#000000, strength=2, direction=135);
}

.banner-large .banner-title {
	position: relative;
	z-index: 101;
	max-width: 300px;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.banner-image {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	width: 100%;
	height: 0;
	padding-bottom: 40%;
	background-position: center top;
	background-repeat: no-repeat;
	background-size: cover;
	box-shadow: inset 0 -20px 40px rgba(0, 0, 0, 0.3);
}

.main-home .banner-image {
	background-position: left top;
}

.banner-download .banner-title {
	margin-bottom: 30px;
}

.banner-download .btn-default {
	color: #ffffff;
	background: transparent;
}

.banner-download .btn-default:hover {
	text-decoration: underline;
	opacity: 1;
}

.banner-download .btn-default small {
	opacity: 0.8;
}

.banner-download-aside {
	position: absolute;
	right: 30px;
	bottom: 0;
	width: 33.333%;
	width: calc(33.333% - 40px);
	padding: 15px 15px 0 15px;
	background: rgba(255, 255, 255, 0.8);
}

.banner-form {
	position: relative;
}

.banner-form .form-control {
	border-color: #d1d3d4;
}

@media screen and (min-width: 768px) {
	.banner-image {
		padding-bottom: 30%;
	}
	.banner-content {
		padding: 30px;
		padding-top: 0;
	}
	.banner-title {
		font-size: 30px;
		line-height: 1;
	}
	.banner-large .banner-title {
		font-size: 34px;
		max-width: 700px;
		line-height: 1.2;
	}
	.banner-download .banner-title {
		max-width: 500px;
	}
}

@media screen and (min-width: 992px) {
	.banner-image {
		height: 200px;
		padding-bottom: 10%;
	}
	.banner-large .banner-image {
		height: 260px;
		padding-bottom: 12%;
	}
	.banner-download .banner-content {
		padding-bottom: 4%;
	}
	.banner-title {
		font-size: 40px;
		max-width: 70%;
	}
	.banner-large .banner-title {
		font-size: 36px;
		line-height: 41px;
		max-width: 50%;
	}
	.banner-large.banner-download .banner-title {
		max-width: 50%;
	}
	.banner-form {
		position: absolute;
		bottom: 0;
		right: 0;
		left: 0;
	}
	.banner-form .col-md-12 {
		z-index: 102;
		width: 33.33333%;
		float: right;
	}
}

@media screen and (max-width: 767px) {
	.banner-large .banner-image {
		min-height: 200px;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
	}
	.banner-form {
		background: #ffffff;
	}
	.banner-download .banner-title {
		margin-bottom: 15px;
	}
	.banner-download .btn-primary {
		width: auto;
		display: block;
	}
	.banner-download .btn-default {
		padding: 0;
		font-size: 14px;
		line-height: 24px;
		margin-top: 10px;
	}
	.banner-download-aside {
		display: none !important;
		visibility: hidden;
	}
}

.main {
	position: relative;
	margin: 0;
	background: transparent;
	overflow-x: hidden;
}

.main > .container {
	padding-top: 30px;
	padding-bottom: 30px;
	background: #ffffff;
}

.main > .container + .container {
	background: #ebfbf6;
}

@media screen and (max-width: 767px) {
	.main {
		padding-top: 55px;
	}
}

@media screen and (min-width: 992px) {
	.main {
		padding-top: 0;
	}
}

.main-single {
	padding-top: 0 !important;
	min-height: 100%;
	background: #ffffff;
}

.main-single > .container {
	padding-top: 0;
}

.main-search .search {
	position: absolute !important;
	display: block !important;
	visibility: visible !important;
	top: 0;
	width: 100%;
	background: transparent;
}

.main-search .search > .container {
	padding: 0 !important;
}

.js .is-nav .main-search .search,
.js .is-nav-active .main-search .search {
	position: absolute !important;
	display: block !important;
	visibility: visible !important;
}

.lt-ie9 .main-search .search .form-group {
	width: 970px;
	max-width: none;
}

@media screen and (max-width: 991px) {
	.main-search .search {
		top: 0px !important;
	}
}

@media screen and (max-width: 767px) {
	.main-search .search {
		top: 55px !important;
	}
}

main.main-search > .container {
	padding-top: 75px;
}

@media screen and (min-width: 768px) {
	main.main-search > .container {
		padding-top: 115px;
	}
}

.main-home .latest {
	margin-bottom: 30px;
}

.main-home .latest h2 {
	color: #005961;
}

.main-home .signpost {
	margin-bottom: 5px;
}

.modal .main-programme,
.modal .main-practitioner {
	padding-top: 0 !important;
	overflow: hidden;
}

.modal .main-programme .breadcrumb,
.modal .main-programme .breadcrumb-wrapper,
.modal .main-practitioner .breadcrumb,
.modal .main-practitioner .breadcrumb-wrapper {
	display: none !important;
	visibility: hidden;
}

.modal .main-programme .return-link,
.modal .main-practitioner .return-link {
	display: none !important;
	visibility: hidden;
}

.main-practitioner .practitioner table th:last-child,
.main-practitioner .practitioner table td:last-child {
	width: 20%;
}

.main-employer-results .practitioner table th,
.main-employer-results .practitioner table td {
	width: 33%;
}

.main-employer-results .practitioner-meta + .table,
.main-employer-results .practitioner-meta + .table-responsive {
	margin-top: -1px;
}

.main-news .module.social {
	margin-bottom: 40px;
}

.main-news h1 + time {
	margin-bottom: 10px;
}

@media screen and (max-width: 767px) {
	.main-hub .aside {
		margin-bottom: 0;
	}
	.main-hub .aside + .aside {
		border-top: 0;
	}
	.main-hub .aside.news,
	.main-hub .aside.subscribe {
		padding-top: 20px;
		padding-bottom: 20px;
	}
	.main-hub .aside.social {
		display: none !important;
		visibility: hidden;
	}
	.main-home .banner-container {
		padding-bottom: 40% !important;
	}
}

@media screen and (max-width: 991px) {
	.main-home .banner-container {
		min-height: 200px;
		height: 0;
		top: 0;
		bottom: auto;
		padding-bottom: 30%;
		background-position: center bottom;
		background-repeat: no-repeat;
		background-size: 100% 60%;
	}
	.main-home .learnmore {
		border: 0;
		clip: rect(0 0 0 0);
		height: 1px;
		margin: -1px;
		overflow: hidden;
		padding: 0;
		position: absolute;
		width: 1px;
	}
}

@media screen and (min-width: 768px) {
	.main-hub .aside {
		margin-bottom: 20px;
	}
	.main-register .results td {
		vertical-align: middle !important;
	}
	.main-programmes .results tbody td {
		padding: 16px 8px;
	}
	.main-programme .breadcrumb-wrapper,
	.main-practitioner .breadcrumb-wrapper {
		margin-bottom: 40px;
	}
}

@media screen and (min-width: 992px) {
	.main-home .banner + .container > .row:first-child {
		margin-bottom: 0;
	}
	.main-home .signpost {
		margin-top: -25px;
	}
	.main-home .signpost h2 {
		padding: 25px 15px;
		margin-bottom: 5px;
		background: #d1d3d4;
	}
	.main .banner + .container > .row:first-child .subnav:first-child {
		margin-top: -106px;
	}
}

/**!
 * jQuery Cookie Cuttr
 */

/* To be used with cookieCuttr by Chris Wharton (http://cookiecuttr.com) */

.cc-cookies {
	width: 100%;
	padding: 10px 5%;
	background: #31006f;
	color: #fff;
	font-size: 14px;
	font-family: 'Gotham bold', Arial, sans-serif;
	text-align: center;
	border-top: 1px solid #00838f;
}

.cc-cookies a,
.cc-cookies a:hover {
	color: #fff;
	text-decoration: underline;
}

.cc-cookies a:hover {
	text-decoration: none;
}

.cc-overlay {
	height: 100%;
	padding-top: 25%;
}

.cc-cookies-error {
	float: left;
	width: 90%;
	text-align: center;
	margin: 1em 0 2em 0;
	background: #fff;
	padding: 2em 5%;
	border: 1px solid #ccc;
	font-size: 18px;
	color: #333;
}

.cc-cookies a.cc-cookie-accept,
.cc-cookies-error a.cc-cookie-accept,
.cc-cookies a.cc-cookie-decline,
.cc-cookies-error a.cc-cookie-decline,
.cc-cookies a.cc-cookie-reset {
	display: inline-block;
	color: #fff;
	text-decoration: none;
	background: #7daf3b;
	padding: 0.5em 0.75em;
	margin: 0 0.5em;
	-webkit-transition: background 100ms ease;
	transition: background 100ms ease;
}

.cc-cookies a.cc-cookie-decline,
.cc-cookies-error a.cc-cookie-decline {
	background: #af3b3b;
	margin-left: 0.5em;
}

.cc-cookies a.cc-cookie-accept {
	background: #d72882;
}

.cc-cookies a.cc-cookie-reset {
	background: #f15b00;
}

.cc-cookies a:hover.cc-cookie-accept,
.cc-cookies-error a:hover.cc-cookie-accept,
.cc-cookies a:hover.cc-cookie-decline,
.cc-cookies-error a:hover.cc-cookie-decline,
.cc-cookies a:hover.cc-cookie-reset {
	background: #00838f;
}

.cc-cookies-error a.cc-cookie-accept,
.cc-cookies-error a.cc-cookie-decline {
	display: block;
	margin-top: 1em;
}

.cc-cookies.cc-discreet {
	width: auto;
	padding: 0.5em 1em;
	left: auto;
	top: auto;
}

.cc-cookies.cc-discreet a.cc-cookie-reset {
	background: none;
	text-shadow: none;
	padding: 0;
	text-decoration: underline;
}

.cc-cookies.cc-discreet a:hover.cc-cookie-reset {
	text-decoration: none;
}

@media screen and (max-width: 767px) {
	.cc-cookies {
		z-index: 99999;
		position: absolute;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		-webkit-transition: left 400ms ease;
		transition: left 400ms ease;
	}
	.dcnav-root.is-nav .cc-cookies {
		left: 260px;
	}
	.cc-cookies a.cc-cookie-accept,
	.cc-cookies a.cc-cookie-decline,
	.cc-cookies a.cc-cookie-reset {
		display: block;
		max-width: 300px;
		margin: 1em auto;
	}
}

.mobile-inline-block {
	display: inline-block;
}

.desktop-inline-block {
	display: none;
}

@media (min-width: 768px) {
	.mobile-inline-block {
		display: none;
	}

	.desktop-inline-block {
		display: inline-block;
	}
}

#CybotCookiebotDialog {
	background: #00749b !important;
	box-shadow: 2px 2px 14px 2px #ccc !important;
	font-family: 'Gotham book', Arial, sans-serif !important;
	color: #fff !important;
}

#CybotCookiebotDialog a {
	color: #fff !important;
}

#CybotCookiebotDialogBodyContentTitle {
	color: #fff !important;
	font-family: 'Gotham bold', Arial, sans-serif !important;
}

#CybotCookiebotDialogBodyButtons .CybotCookiebotDialogBodyLink,
#CybotCookiebotDialogBodyLevelDetailsButton {
	color: #fff !important;
}

#CybotCookiebotDialog a#CybotCookiebotDialogBodyButtonAccept {
	background: #74e0c1 !important;
	border-color: #74e0c1 !important;
	font-family: 'Gotham bold', Arial, sans-serif !important;
	color: #111 !important;
}

#CybotCookiebotDialogBodyButtons .CybotCookiebotDialogBodyLink,
#CybotCookiebotDialogBodyLevelDetailsButton {
	background-image: none !important;
}

#CybotCookiebotDialogBodyButtons .CybotCookiebotDialogBodyLink:after,
#CybotCookiebotDialogBodyLevelDetailsButton:after {
	content: '';
	background: url(/static/img/refresh/chevron-white.svg) no-repeat 8px 0px !important;
	transform: rotate(90deg) !important;
	width: 18px;
	height: 15px;
	display: inline-block;
}

#CybotCookiebotDialogDetail {
	background: #00749b !important;
}

.CybotCookiebotDialogDetailBodyContentTabsItemSelected,
#CybotCookiebotDialogDetailBodyContentCookieContainerTypes,
#CybotCookiebotDialogDetailBodyContentIABv2Tabs,
#CybotCookiebotDialogDetailFooter,
.CybotCookiebotDialogDetailBodyContentCookieContainerTypesSelected,
.CybotCookiebotDialogDetailBodyContentIABv2TabSelected,
#CybotCookiebotDialogDetailBodyContent {
	background: #00749b !important;
}

.CybotCookiebotDialogDetailBodyContentCookieContainerTypesSelected,
.CybotCookiebotDialogDetailBodyContentIABv2TabSelected,
.CybotCookiebotDialogDetailBodyContentCookieContainerTypesSelected:first-child {
	border-color: #cccccc !important;
}

.CybotCookiebotDialogDetailBodyContentCookieContainerTypes,
.CybotCookiebotDialogDetailBodyContentIABv2Tab,
.CybotCookiebotDialogDetailBodyContentTabsItem,
.CybotCookiebotDialogDetailBodyContentCookieTypeTable thead td,
.CybotCookiebotDialogDetailBodyContentCookieTypeTable thead th {
	background-color: #015d7b !important;
}

.no-border {
	border: none !important;
}

.m-0 {
	margin: 0 !important;
}

.inline-block {
	display: inline-block;
}

@media print {
	html,
	body {
		overflow: visible !important;
		height: auto !important;
	}
	.cc-cookies,
	.header-notice {
		display: none !important;
	}
	svg[role='presentation'] {
		display: none !important;
	}
	hr,
	.hr-bold {
		margin: 0 !important;
		display: none !important;
	}
	a,
	.prose a,
	.prose p a,
	a span {
		color: #d72882 !important;
	}
	/* TODO */
	a[href]:after {
		/* content: none !important; */
		color: #d72882 !important;
		/* display: none; */
	}
	*,
	.prose {
		color: #000000 !important;
		background: transparent !important;
		font-size: 16px !important;
		line-height: 1.2 !important;
	}
	* a,
	* a:focus,
	* a:hover,
	* a:active,
	.prose a,
	.prose a:focus,
	.prose a:hover,
	.prose a:active {
		color: #d72882 !important;
		text-decoration: underline;
	}
	* h1,
	* .h1,
	.prose h1,
	.prose .h1 {
		font-size: 30px !important;
		line-height: 1.2 !important;
	}
	* h2,
	* .h2,
	.prose h2,
	.prose .h2 {
		font-size: 24px !important;
		line-height: 1.2 !important;
	}
	* h3,
	* .h3,
	.prose h3,
	.prose .h3 {
		font-size: 20px !important;
		line-height: 1.2 !important;
	}
	* h5,
	* h6,
	* .h5,
	* .h6,
	.prose h5,
	.prose h6,
	.prose .h5,
	.prose .h6 {
		font-size: 18px !important;
		line-height: 1.2 !important;
	}
	* p a[href]:after,
	* li a[href]:after,
	.prose p a[href]:after,
	.prose li a[href]:after,
	* a[href]:after {
		content: ' (' attr(href) ')';
		color: #d72882 !important;
		text-decoration: none !important;
		font-size: 14px;
		word-wrap: break-word;
		word-break: break-word;
	}
	* small,
	.prose small {
		font-size: 14px !important;
	}
	* blockquote,
	.prose blockquote {
		border: 0 !important;
		padding: 0 !important;
	}
	* .table,
	.prose .table {
		font-size: 14px !important;
	}
	* .table th,
	.prose .table th {
		font-size: 16px !important;
	}
	* .table-bordered,
	.prose .table-bordered {
		border-color: #b2b6b7;
	}
	.header {
		*zoom: 1;
		overflow: visible !important;
		position: relative !important;
		height: 100px !important;
	}
	.header:before,
	.header:after {
		content: '';
		display: table;
	}
	.header:after {
		clear: both;
	}
	.header .logo {
		float: right !important;
		margin: 0 !important;
		width: 165px !important;
		height: 80px !important;
	}
	.header .logo img {
		display: block !important;
		max-width: 105px !important;
		margin-left: auto;
	}
	.header-bar,
	.header-links,
	.header-feature {
		display: none !important;
	}
	.dcnav,
	.search,
	.main-search .search {
		display: none !important;
	}
	.banner {
		width: auto !important;
		height: auto !important;
	}
	.banner h1 {
		font-size: 30px !important;
		max-width: none !important;
	}
	.banner-content,
	.banner-container {
		position: relative !important;
		top: auto !important;
		padding: 0 !important;
	}
	.banner-image,
	.banner-form,
	.main-banner-overlay {
		display: none !important;
	}
	.container,
	*[class*='col-'] {
		padding: 0 !important;
	}
	.row {
		margin-left: 0 !important;
		margin-right: 0 !important;
	}
	.main {
		height: auto !important;
		padding-top: 0 !important;
	}
	.main > .container {
		padding: 0 !important;
		border: 0 !important;
		margin-bottom: 30px !important;
	}
	.media-object {
		max-width: 100px !important;
	}
	.practitioner dl {
		border: 0 !important;
		padding: 0 !important;
		margin: 0 0 5px 0 !important;
	}
	.practitioner .table-bordered {
		border: 0 !important;
		border-top: 1px solid #b2b6b7 !important;
		margin-bottom: 15px !important;
	}
	.practitioner .table-bordered td,
	.practitioner .table-bordered th {
		padding: 0 5px 5px 0 !important;
	}
	.practitioner .table-bordered th {
		padding-top: 10px !important;
	}
	.practitioner .table-bordered thead,
	.practitioner .table-bordered tbody,
	.practitioner .table-bordered td,
	.practitioner .table-bordered th,
	.practitioner .table-bordered tr {
		border: 0 !important;
	}
	.practitioner + .notice {
		border-top: 1px solid #b2b6b7 !important;
		padding-top: 10px;
	}
	.main-employer-results .practitioner .table-bordered {
		border-top: 0 !important;
	}
	.main-employer-results .practitioner + .hr-medium {
		display: block !important;
		height: 0 !important;
		margin: 20px 0 !important;
		border-top: 1px solid #b2b6b7 !important;
		background: transparent !important;
	}
	.practitioner-status {
		border: 0;
		padding: 0;
		margin: 15px 0 30px 0 !important;
	}
	.practitioner-status .lead {
		margin-bottom: 15px;
	}
	.practitioner-status .media-object {
		display: none;
	}
	.practitioner-status .media-body {
		margin: 0;
	}
	.results-positive .results-status:before {
		content: '✔';
	}
	.results-negative .results-status:before {
		content: '✘';
	}
	.results-caution .results-status:before {
		content: '?';
		font-weight: bold;
	}
	.notice {
		border: 0;
		padding: 0;
	}
	.inline-form,
	.programmes-form,
	.employer-form,
	.register-form,
	.logout-form,
	.btn-more,
	.more-link,
	.return-link,
	.breadcrumb-wrapper,
	.breadcrumb,
	.carousel,
	.news,
	.news-categories,
	.paginated,
	.sections,
	.signpost,
	.share,
	.social,
	.subscribe,
	.subnav {
		display: none !important;
	}
	.footer-prose,
	.footer-social,
	.footer-primary,
	.footer-secondary {
		display: none !important;
	}
	.footer-small {
		margin: 0 !important;
		color: #000 !important;
		font-size: 16px !important;
		line-height: 1.2 !important;
		opacity: 1 !important;
	}

	.navigation-header-mobile,
	.document-navigation,
	.ftp-search,
	.related-guides,
	.icon,
	.topic-icon {
		display: none !important;
		visibility: hidden !important;
	}
	/* FtP Guides */

	.policy-listings .topic {
		padding: 30px;
	}

	.policy-listings .topic p {
		margin: 0;
	}

	.policy-listings .title-bar .print {
		display: none !important;
	}
	.policy-listings .guide-nav {
		display: none;
	}
	.policy-listings .back-to-top-link {
		display: none;
	}
	.policy-listings sup {
		font-size: 50% !important;
	}
	.navigation-header-desktop {
		border: none !important;
	}

	.policy-listings a[href]:after {
		content: none !important;
	}

	/*hiding content */
	.no-print {
		display: none !important;
	}

	a.removePrint[href]:after {
		content: none !important;
	}

	.show-print {
		display: block !important;
		height: auto !important;
	}

	.statistics-table thead {
		display: table-header-group !important;
	}

	.statistics-table thead tr {
		display: table-row !important;
	}

	.statistics-table th {
		display: table-cell !important;
	}

	.statistics-table thead,
	.statistics-table thead tr,
	.statistics-table th {
		padding: 8px !important;
	}
}

/* Ajax Loader
######################################################################*/

/* Absolute Center CSS Spinner */

#loading {
	position: fixed;
	z-index: 999;
	height: 2em;
	width: 2em;
	overflow: show;
	margin: auto;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}

/* Transparent Overlay */

#loading:before {
	content: '';
	display: block;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.3);
}

/* :not(:required) hides these rules from IE9 and below */

#loading:not(:required) {
	/* hide "loading..." text */
	font: 0/0 a;
	color: transparent;
	text-shadow: none;
	background-color: transparent;
	border: 0;
}

#loading:not(:required):after {
	content: '';
	display: block;
	font-size: 10px;
	width: 1em;
	height: 1em;
	margin-top: -0.5em;
	-webkit-animation: spinner 1500ms infinite linear;
	-moz-animation: spinner 1500ms infinite linear;
	-ms-animation: spinner 1500ms infinite linear;
	-o-animation: spinner 1500ms infinite linear;
	animation: spinner 1500ms infinite linear;
	border-radius: 0.5em;
	-webkit-box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0,
		rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0,
		rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
	box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0,
		rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) -1.5em 0 0 0, rgba(0, 0, 0, 0.75) -1.1em -1.1em 0 0,
		rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
}

/* Home page
######################################################################*/

.main-home .share.share-small {
	margin-bottom: 40px;
}

/* Body amends
######################################################################*/

@media screen and (min-width: 992px) {
	html,
	body {
		line-height: 1.5;
	}
	.block.aside.news,
	.media-body p,
	.form-group {
		line-height: 1.35;
	}
	#notice-accordion .pull-left {
		margin-top: 4px;
	}
}

p {
	margin: 0 0 15px;
}

.prose ul li {
	margin-bottom: 15px;
}

.main > .container {
	padding-top: 25px;
}

.modal-content main.main-practitioner {
	display: none;
}

/* Heading amends
######################################################################*/

h1,
.h1 {
	font-size: 30px;
}

h2,
.h2 {
	font-size: 24px;
}

[class*='col-'] > .lead {
	margin-bottom: 5px;
}

@media screen and (min-width: 768px) {
	h1,
	.h1 {
		font-size: 36px;
	}
	h2,
	.h2 {
		font-size: 30px;
	}
	[class*='col-'] > .lead {
		margin-bottom: 20px;
	}
}

@media screen and (min-width: 992px) {
	h1,
	.h1 {
		font-size: 40px;
	}
}

/* episerver forms custom classes
######################################################################*/

.Form__MainBody {
	border: 1px solid #d1d3d4;
	padding: 15px;
}

form.EPiServerForms {
	margin-bottom: 30px;
}

form.EPiServerForms .form_footer_section {
	border-top: 1px solid #d1d3d4;
	margin-top: 30px;
	margin-left: -16px;
	margin-right: -16px;
	padding: 5px 16px 25px;
	min-height: 50px;
}

form.EPiServerForms .form_footer_section .FormSubmitButton {
	margin-top: 17px;
}

.EPiServerForms .form_footer_section.inEditor {
	margin: 0;
	padding: 0;
	height: 50px;
}

form.EPiServerForms .form_footer_section p {
	padding-top: 7px;
}

.required_field_notice {
	padding-bottom: 20px;
	margin-top: -15px;
}

.EPiServerForms .checkbox-group .checkbox {
	width: 30%;
}

.lt-ie10 .form_ie8_message {
	display: block;
}

.lt-ie10 .EPiServerForms.form {
	display: none;
}

.Form__Status.form_ie8_message {
	padding-bottom: 20px;
}

/* errors */

.Form__MainBody .ValidationFail input:not(.notRequired),
.Form__MainBody .ValidationFail textarea:not(.notRequired),
.Form__MainBody .ValidationFail select:not(.notRequired) {
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
	background-color: #fffbcc;
	border-color: #b44113;
	border-width: 2px;
}

.EPiServerForms .Form__Element .Form__Element__ValidationError {
	margin-top: 5px;
	color: #d00000 !important;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 16px;
}

/* success message */

.EPiServerForms .Form__Status .Form__Status__Message.Form__Success__Message {
	background-color: #ecf3e7;
	padding-left: 85px;
	padding-bottom: 20px;
	padding-top: 20px;
	min-height: 100px;
}

.EPiServerForms .Form__Status .success_message_tick .success_message_tick_image {
	margin-top: 25px;
	margin-left: 25px;
}

.EPiServerForms .Form__Status .Form__Status__Message.Form__Success__Message h2,
.EPiServerForms .Form__Status .Form__Status__Message.Form__Success__Message h3,
.EPiServerForms .Form__Status .Form__Status__Message.Form__Success__Message p.lead {
	font-size: 24px;
	font-weight: 300;
	color: #438516;
}

/* multi step navigation */

.Form__NavigationBar,
bottom-navigation-bar {
	width: 100%;
}

.EPiServerForms .Form__MainBody .Form__NavigationBar .Form__NavigationBar__ProgressBar {
	display: none;
}

.form-step-navigation.bottom-navigation {
	border-left: 1px solid #d1d3d4;
	border-bottom: 1px solid #d1d3d4;
	border-right: 1px solid #d1d3d4;
	padding: 15px 16px;
}

.form-step-navigation.top-navigation {
	border-left: 1px solid #d1d3d4;
	border-top: 1px solid #d1d3d4;
	border-right: 1px solid #d1d3d4;
	padding: 15px 16px;
}

.EPiServerForms .Form__NavigationBar .form-step-navigation .Form__NavigationBar__ProgressBar {
	width: 100%;
}

.EPiServerForms .Form__NavigationBar div.Form__NavigationBar__ProgressBar {
	background-color: #eff0f0;
	height: 10px;
	margin-bottom: 15px;
	border: medium none;
}

.EPiServerForms .Form__NavigationBar .Form__NavigationBar__ProgressBar div.Form__NavigationBar__ProgressBar--Progress {
	background-color: #00838f;
	height: 10px;
}

.Form__NavigationBar__ProgressBar--Text {
	font-weight: 700;
	margin-bottom: 5px;
}

.EPiServerForms .form-step-navigation .action-link-container .action-group {
	display: block;
}

.bottom-navigation-bar .form-step-navigation .next-step {
	float: right;
}

.bottom-navigation-bar .form-step-navigation .prev-step {
	float: left;
}

.bottom-navigation-bar .form-step-navigation .action-group-item {
	margin-right: 0px;
}

@media all and (max-width: 768px) {
	.main-content .EPiServerForms .bottom-navigation-bar .Form__NavigationBar__Action.btn {
		background-color: red;
		font-size: 14px;
	}
}

/*have to override a lot of styles from forms css / bootstrap */

.main-content .EPiServerForms .bottom-navigation-bar .Form__NavigationBar__Action {
	background-image: none;
	border: 1px solid #d1d3d4;
	border-radius: 0px;
	color: #00334c;
	display: inline-block;
	font-size: 18px;
	height: auto;
	max-width: none;
	padding: 10px 16px;
	text-align: inherit;
	vertical-align: inherit;
	width: auto;
}

/* info box */

.info-desk {
	padding: 24px 20px;
	font-size: 18px;
	line-height: 27px;
	float: none !important;
	position: initial;
	background-color: #eafbf6;
	border: none;
	border-left: 16px solid #74e0c1;
	clear: both;
	display: inline-block !important;
	margin-bottom: 25px;
	width: 100%;
}

@media screen and (max-width: 767px) {
	.container-form .info-desk {
		padding: 20px 0 !important;
	}
	.container-form .info-desk p {
		padding: 0 !important;
	}
	.container-form .info-desk ul {
		padding-left: 20px !important;
	}
}

.info-desk ul {
	padding-left: 20px;
}

.info-desk ul li {
	margin-bottom: 20px;
}

.info-desk svg,
.info-desk img {
	max-width: 44px !important;
	max-height: 44px !important;
	display: inline-block;
}

/* Ftp page classes
######################################################################*/

@-ms-viewport {
	width: device-width;
}

.container-general {
	/*font-family: FoundryMonoline;*/
	color: #000011;
}

.container-general .action-group .col-lg-10,
.container-general .action-group .col-md-10,
.container-general .action-group .col-sm-10 {
	width: 90%;
}

.container-general .links-general .row {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
}

.container-general .links-general .row > [class*='col-'] {
	display: flex;
	flex-direction: column;
}

.container-general .action-group-item-opening .frame {
	float: left;
	height: 100%;
	/* equals max image height */
	width: 100%;
	white-space: nowrap;
}

.container-general .action-group-item .ico-opening {
	display: inline;
	vertical-align: middle !important;
}

.container-general .action-group .action-group-item-opening a h2 {
	width: 100%;
	position: relative;
	display: block;
	padding: 15px 15px;
	font-weight: bold !important;
	color: #fff;
	-webkit-transition: background-color 150ms ease;
	transition: background-color 150ms ease;
	border-radius: 0;
	white-space: normal;
}

.container-general .action-group .action-group-item-opening a::after {
	position: absolute;
	display: block;
	content: '';
	top: 0;
	right: 10px;
	width: 30px;
	height: 90%;
	background: url(/static/img/arrow-right.svg) center center no-repeat;
	background-size: 25px auto;
}

.container-general ul {
	list-style: disc;
}

.container-general .action-group-item-opening .accordion-head {
	cursor: pointer;
}

.container-general .action-group-item-opening {
	display: table;
}

.container-general.action-group-item-opening .text {
	display: table-row;
}

.container-general .action-group-item-opening span {
	width: 100%;
}

.container-general .action-group-item-opening .text div {
	line-height: 99px;
	height: 100%;
	text-align: left;
	color: #00334c;
	white-space: normal;
}

.container-general .action-group-item-opening .accordion-body ul {
	padding-bottom: 15px;
}

.container-general .action-group-item-opening .text div h2 {
	vertical-align: middle;
	line-height: 34px;
	text-align: left;
	color: #00334c;
	display: inline-block;
	font-weight: bold !important;
	padding-right: 30px;
	padding: 10px 30px 10px 0;
}

.container-general .action-group-item-opening .text div .close-icon::after {
	background: url(/static/img/close_icon.svg) center center no-repeat !important;
}

.container-general .action-group-item-opening .text div h2::after {
	position: absolute;
	display: block;
	content: '';
	top: 0;
	right: 10px;
	width: 30px;
	height: 100%;
	background: url(/static/img/plus_icon.svg) center center no-repeat;
	background-size: 25px auto;
}

.container-general .action-group-item {
	height: auto !important;
	margin-bottom: 25px;
	min-height: 140px;
	width: 100%;
}

@media screen and (min-width: 1200px) {
}

.container-general .action-group-item .col-md-2,
.container-general .action-group-item .col-sm-2 {
	width: 10% !important;
}

.container-general .action-group-item .col-md-10 .container-general .action-group-item .col-sm-10 {
	width: 90% !important;
}

@media screen and (max-width: 991px) {
	.container-general .action-group-item .col-sm-2 {
		width: 72px !important;
	}
	.container-general .action-group-item .col-sm-10 {
		width: calc(100% - 72px) !important;
	}
	.container-general .row > [class*='col-'] {
		margin: 0;
	}
}

.container-general .action-group {
	width: 100%;
	display: block !important;
}

.container-general .action-group-item .color img {
	display: inline;
	width: 30px;
	margin: 0;
}

.container-general .action-group-item .color {
	width: 100%;
	text-align: center;
	float: left;
	padding: 32px 17px;
	min-height: 140px;
}

.container-general .action-group-item .text {
	min-height: 140px;
	width: 100% !important;
	border-top: 1px solid #d1d3d4;
	border-right: 1px solid #d1d3d4;
	border-bottom: 1px solid #d1d3d4;
	padding-right: 20px;
}

.container-general .action-group-item .text span {
	vertical-align: middle;
	padding-left: 37px;
	font-size: 26px;
	text-align: left;
	line-height: 30px;
	color: #00334c;
	white-space: normal;
	min-height: 140px;
	margin-bottom: 150px;
}

.container-general .btn-block p {
	padding-left: 33px;
	text-align: left;
	line-height: 86px;
	font-size: 26px;
	color: #fff !important;
	white-space: normal;
}

.container-general .media-heading {
	font-weight: normal !important;
}

.container-general .action-group-item .text .title {
	display: block;
	padding-left: 40px;
	padding-top: 28px;
	font-size: 26px;
	text-align: left;
	margin: 0;
	min-height: 1px;
}

.container-general .action-group-item .text .title a {
	color: #331272 !important;
	white-space: normal;
}

.container-general .action-group-item .text .title a:hover {
	color: #331272 !important;
	text-decoration: underline;
}

.container-general .action-group-item .text .title::before {
	display: inline-block;
	vertical-align: top;
	content: '';
	width: 16px;
	height: 26px;
	background: url(../img/refresh/chevron.svg) 0 center no-repeat;
	background-size: auto 18px;
	margin-left: -15px;
	margin-right: 4px;
}

.container-general .action-group-item .text .body-text {
	display: block;
	font-size: 18px;
	line-height: 27px;
	font-weight: normal;
	padding: 12px 12px 12px 30px;
	text-align: left;
	color: #000011;
	white-space: normal;
	margin: 0;
}

.container-general .action-group-item-opening .text span,
.container-general .action-group-item-opening .text,
.container-general .action-group-item-opening .color,
.container-general .action-group-item-opening {
	height: 100%;
	min-height: 1px;
}

.flexbox .action-link-container .action-group-item {
	padding: 0 15px;
}

.container-general .action-group-item .row {
	padding: 0;
}

.container-general .action-group-item .row > [class*='col-'] {
	padding: 0;
}

.container-general .action-group-item .btn-block {
	padding-left: 33px;
	text-align: left;
	line-height: 86px;
	font-size: 26px;
	color: #fff !important;
}

.container-general .action-group-item .btn-block:focus {
	background: #00334c !important;
}

.container-general .open-text {
	padding: 25px;
	font-size: 18px;
	font-weight: normal;
	line-height: 24px;
	text-align: left;
	white-space: normal;
	border-left: 1px solid #d1d3d4;
	border-right: 1px solid #d1d3d4;
	border-bottom: 1px solid #d1d3d4;
}

.container-general .open-text a {
	font-size: 18px;
	padding: 0;
	font-weight: bold;
	text-decoration: underline;
	color: #d10074 !important;
}

@media screen and (max-width: 767px) {
	.container-general .action-group-item {
		min-height: 0;
		margin-bottom: 0 !important;
	}
	.container-general .action-group-item .text {
		border-top: none;
		border-right: none;
		display: block;
		position: relative;
		float: none;
		min-height: 0;
	}
	.container-general .action-group-item .text .title {
		padding-top: 20px;
		padding-left: 28px;
	}
	.container-general .action-group-item .text .title::before {
		height: 26px;
	}
	.container-general .action-group .col-xs-12 {
		width: 100% !important;
	}
	.container-general .btn-block p {
		line-height: 26px;
		margin: 0px;
	}
	.container-general .action-group-item .btn-block {
		line-height: normal;
		padding-left: 0;
	}
	.container-general .call-to-action .action-group-item .btn-block {
		margin: 20px 0px;
	}
	.container-general .action-group-item .text p {
		display: block !important;
		padding: 15px !important;
		font-size: 14px;
	}
	.container-general .action-group .action-group-item-opening a {
		font-size: 18px;
	}
	.container-general .action-group-item-opening .text div h2,
	.container-general .action-group-item-opening .text div {
		line-height: normal;
	}
	.container-general .action-group-item-opening {
		min-height: 55px;
		height: 100%;
		overflow: hidden;
	}
	.container-general .action-group-item-opening .open-text {
		display: block;
		float: none;
		padding-top: 20px !important;
		border-left: none;
		border-right: none;
	}
	.container-general .action-group.call-to-action {
		border-top: none;
	}
	.container-general .action-group {
		border-top: 1px solid #d1d3d4;
	}
	.container-general .action-group .action-group-item-opening a h2 {
		font-size: 18px;
	}
}

.action-link-container .bg-Black {
	background-color: #111111;
}

.action-link-container .bg-Green {
	background-color: #331272;
}

.action-link-container .bg-Grey {
	background-color: #331272;
}

.action-link-container .bg-Blue {
	background-color: #331272;
}

.action-link-container .bg-Orange {
	background-color: #331272;
}

.action-link-container .bg-LightBlue {
	background-color: #331272;
}

.action-link-container .bg-Lavender {
	background-color: #331272;
}

.action-link-container .bg-DarkBlue {
	background-color: #331272;
}

.action-link-container .bg-Pink {
	background-color: #331272;
}

.row-eq-height {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
}

/* Statistics Page
######################################################################*/

.statistics-table .notice-toggle {
	float: right;
	cursor: pointer;
	display: block;
	height: 20px;
	width: 20px;
}

.statistics-table .notice-toggle svg {
	fill: #666d70;
	height: 20px;
	width: 20px;
}

.statistics-table .notice:after {
	content: '';
	display: block;
	height: 0;
	left: 100%;
	position: absolute;
	top: 15px;
	width: 0;
}

.results .statistics-table th:last-child {
	text-align: left;
}

.results .statistics-table td:last-child {
	text-align: left;
	font-weight: normal;
}

.statistics-form .programmes-form-submit {
	margin-top: 0px;
}

.statistics-form .statistics-year {
	width: 30%;
}

.statistics-form .statistics-type {
	width: 70%;
}

@media all and (max-width: 768px) {
	.statistics-form .statistics-year {
		width: 100%;
	}
	.statistics-form .statistics-type {
		width: 100%;
	}
}

.statistics-table .results-col-interval {
	width: 40%;
}

.statistics-table .results-col-stats {
	width: 15%;
}

.statistics-table .registration-message {
	float: right;
}

.statistics-table .popover .popover-content {
	font-size: 14px;
	font-weight: normal;
}

.statistics-table .prose.statistics-message p {
	font-size: 16px;
}

/* Banner font sizes
######################################################################*/

.banner-title {
	font-size: 30px;
	text-shadow: 1px 1px 3px black;
}

.banner-large .banner-title {
	font-size: 20px;
	max-width: 100%;
}

@media screen and (min-width: 768px) {
	.banner-title {
		font-size: 34px;
		line-height: 1;
	}
	.banner-large .banner-title {
		font-size: 30px;
		max-width: 100%;
		line-height: 1.2;
	}
}

@media screen and (min-width: 992px) {
	.banner-title {
		font-size: 48px;
		line-height: 1.1;
		max-width: 60%;
	}
	.banner-large .banner-title {
		font-size: 34px;
		line-height: 45px;
		max-width: 65%;
	}
}

/* Signpost
######################################################################*/

.signpost h2 {
	margin-left: -15px;
}

@media screen and (min-width: 768px) {
	.signpost h2 {
		margin-left: -5px;
	}
}

@media screen and (min-width: 992px) {
	.signpost h2 {
		margin-left: 0px;
	}
}

.main-home .signpost h2 {
	margin-top: 0px;
	margin-bottom: 0px;
	background-color: #ebfbf6;
	padding: 32px 15px;
	border-top: none;
}

.signpost h2 {
	border: none;
	font-size: 24px;
	line-height: 22px;
	padding: 32px 15px 32px 0;
	margin-bottom: 0px;
	background: #fff;
	border-top: 1px solid #005961;
}

/* Breadcrumb
######################################################################*/

.breadcrumb {
	font-size: 14px;
	margin-bottom: 30px;
}

.media-heading h2 {
	font-size: 28px;
}

/* List groups
######################################################################*/

.list-group-item,
.list-group-item-cookie {
	padding: 10px 15px 3px;
}

.list-group-item a strong,
.list-group-item-cookie a strong {
	font-size: 16px;
}

/* Subnav
######################################################################*/

.subnav a {
	font-size: 16px;
	font-weight: normal;
}

@media screen and (min-width: 992px) {
	.subnav {
		margin-top: 45px;
	}
	.main .banner + .container > .row:first-child .subnav:first-child {
		margin-top: -112px;
	}

	.main .banner + .container > .row:first-child .subnav:first-child h2 {
		background: none;
		padding: 32px 15px;
		border-top: none;
	}
}

/* Make site full width
######################################################################*/

.container,
.banner {
	max-width: 100%;
}

/* Hub
######################################################################*/

.hub .media-heading {
	font-size: 26px;
	line-height: 1;
	padding-left: 20px;
	position: relative;
}

.hub .media-heading::before {
	height: 20px;
	width: 15px;
	position: absolute;
	top: 3px;
	left: 0;
}

/* Carousel
######################################################################*/

.carousel-overlay {
	min-height: 150px;
}

.carousel-title {
	font-size: 24px;
}

.carousel-text p {
	font-size: 16px;
}

.carousel-link {
	color: #ffffff;
	font-weight: bold;
}

.carousel-link:focus {
	color: #ffffff;
}

@media screen and (min-width: 768px) {
	.carousel-title {
		font-size: 24px;
	}
	.carousel-text p {
		font-size: 16px;
	}
}

@media screen and (min-width: 992px) {
	.carousel-title {
		font-size: 32px;
	}
	.carousel-text p {
		font-size: 18px;
	}
}

/* News images on home page
########################################################*/

@media screen and (min-width: 768px) {
	.latest .media {
		border-bottom: 1px solid #d1d3d4;
		margin: 20px 0;
		padding-bottom: 20px;
	}
}

/* Visual feedback on linked images
########################################################*/

article img,
img.carousel-image {
	opacity: 1;
	transition: opacity 150ms ease 0s;
}

article img:hover,
img.carousel-image:hover {
	opacity: 0.8;
}

/* Improved spacing on floated images
########################################################*/

.prose img.pull-left {
	margin-right: 25px;
	margin-top: 8px;
	max-width: 40%;
}

.prose img.media-object {
	margin-top: 0px;
}

/* Full width buttons on small screens
########################################################*/

.aside .btn-block {
	max-width: 100%;
}

/* Extra .ie fixes
########################################################*/

.lt-ie9 .header-links a {
	padding-right: 20px;
}

.lt-ie9 .banner {
	max-width: 1500px;
}

/* Modals
######################################################################*/

.main.is-modal {
	padding-top: 0px;
}

/* Validation
######################################################################*/

.help-block.has-error {
	color: #b44113;
}

.field-validation-valid {
	display: none;
}

.validation-summary-valid {
	display: none;
}

.no-help-block .has-error p.help-block {
	display: none !important;
}

/* Ajax Forms
######################################################################*/

form button.return-link {
	padding: 0;
	border: 0;
	color: #00334c;
	transition: color 100ms ease 0s;
	background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
}

form button.return-link:hover span {
	text-decoration: underline;
}

form button.return-link:hover,
form button.return-link:focus {
	color: #d10074;
}

/* Related documents
######################################################################*/

.related-documents a {
	font-size: 16px;
}

/* Blocks and sidebar content
######################################################################*/

.main-home .signpost {
	margin-bottom: 20px;
}

.block:empty {
	display: none;
	visibility: hidden;
}

.block .alert.alert-error {
	overflow: auto;
}

/* Fix for spacing issue on Search Register page, bottom module*/

#ajaxForm {
	margin-bottom: 30px;
}

#validationSummary + .btn-more {
	border-top: none;
}

.main-employer-results .share {
	margin-top: 0;
	float: none;
}

#pagingButton.btn-more {
	border-top: none;
}

.main-news #pagingButton.btn-more {
	border-top: 1px solid #c6dab9;
}

/* Js-Expanded Notice
######################################################################*/

.js .notice .js-expanded {
	position: relative;
	z-index: 101;
}

.notice-body p strong {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.js .notice .js-expanded:after {
	position: absolute;
	display: block;
	content: '';
	right: 15px;
	top: 50%;
	width: 0;
	height: 0;
	margin-top: -9px;
	border: 5px solid transparent;
	border-bottom-color: #111111;
}

.js .notice .js-expanded.is-collapsed:after {
	margin-top: -3px;
	border-bottom-color: transparent;
	border-top-color: #111111;
}

/* ButtonBackgroundColour
######################################################################*/

.button-bg-blue {
	background-color: #6571b3 !important;
}

.button-bg-pink {
	background-color: #d10074 !important;
}

.button-bg-orange {
	background-color: #b44113 !important;
}

.button-bg-gray {
	background-color: #eff0f0 !important;
}

.button-bg-white {
	background-color: #ffffff !important;
}

.button-bg-gray span {
	color: #000000;
}

.button-bg-orange svg,
.button-bg-orange img,
.button-bg-pink svg,
.button-bg-pink img,
.button-bg-blue svg,
.button-bg-blue img {
	width: 25px;
}

/* Images
######################################################################*/

img.aside-thumb {
	width: 100%;
}

/* Template hint for blocks in preview mode*/

.alert-info-block {
	background-color: #b8c0c5;
	border-color: #b8c0c5;
	box-shadow: 3px 3px 5px #ccc;
	color: #f2f2f2;
	font-family: Verdana;
	font-size: 1em;
	font-style: italic;
	text-align: center;
	border-radius: 4px;
}

/* Search page
######################################################################*/

/* Non JS pagination on search page spacing*/

.module.paginated.search-spacing {
	margin-top: 20px;
}

#search-sort-by-update {
	padding: 7px 16px;
}

.searched-title a em {
	font-weight: bold;
}

p.searched-description em {
	font-weight: bold;
	font-style: normal;
}

.searched-link {
	text-overflow: clip;
}

.searched-item h3 {
	overflow-x: hidden;
}

.search-criteria:valid {
	background-color: #fff;
}

.searched .btn-more {
	margin-top: 0;
}

.notice.paging-notice {
	margin-top: 20px;
}

/* Tables
######################################################################*/

.prose table thead th,
.prose table thead td {
	font-family: 'Gotham bold', Arial, sans-serif;
}

caption p {
	text-align: left;
	padding-bottom: 10px;
}

/* Social media icon spacing
######################################################################*/

.footer-social-media-item {
	background-position: 0 0;
	background-repeat: no-repeat;
	display: block;
	height: 40px;
	margin-right: 20px;
}

/* Table fonts
######################################################################*/

.prose table,
.table {
	font-size: 16px;
}

.prose table {
	font-size: 14px;
}

/* Virtual tour
######################################################################*/

#vtour {
	display: block;
	height: auto;
	max-width: 100%;
}

/* Employer Confirmations
######################################################################*/

.employer-form button.btn-reset {
	float: left;
	background-color: #ffffff;
	color: #00334c;
	border: 1px solid #d1d3d4;
}

/* Search the register
######################################################################*/

.inline-form.register-form .form-body {
	display: block;
}

.search-register-recaptcha {
	padding-top: 14px;
	padding-bottom: 14px;
	padding-left: 5px;
}

.recaptcha-modal .modal-dialog {
	width: 400px;
}

.recaptcha-modal .g-recaptcha {
	padding-left: 8%;
	padding-top: 10px;
}

.form-group.search-recaptcha {
	margin-bottom: 0px;
}

.recaptcha-popup-message {
	font-size: 16px;
	text-align: center;
	padding-bottom: 15px;
	padding-top: 7px;
	padding-left: 10px;
	padding-right: 10px;
}

.recaptcha-popup-message p {
	font-size: 16px;
}

.recaptcha-modal .modal-body {
	position: relative;
	padding: 15px;
	padding-bottom: 45px;
	padding-top: 25px;
}

.recaptcha-modal.modal {
	top: 28%;
}

@media all and (max-width: 768px) {
	.recaptcha-modal .g-recaptcha {
		padding-left: 8px;
	}
	.recaptcha-modal .modal-body {
		padding: 15px;
	}
	.recaptcha-modal .modal-dialog {
		width: 350px;
	}
}

/* Registrant
######################################################################*/

.practitioner-status a.show-conditions-link {
	font-size: 18px;
}

.practitioner .practitioner-status .media-object.show-conditions-link {
	margin-top: 25px;
}

/* Accordion
######################################################################*/

.accordion-item.panel {
	margin: 0 !important;
	border-radius: 0;
}

.accordion-item .panel-heading {
	padding: 0;
}

.accordion-item a {
	display: block;
	padding: 10px 15px;
}

.accordion-item a:focus {
	outline: none;
}

.accordion-item .panel-title .title {
	color: #00334c;
	word-wrap: break-word;
	padding-right: 15px;
}

.accordion-item .panel-title .view-more {
	font-size: 13px;
	position: absolute;
	right: 50px;
	padding-top: 3px;
}

.accordion-item .accordion-arrow-container {
	vertical-align: middle;
	float: right;
}

.accordion-item .accordion-arrow {
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-bottom: 5px solid #666d70;
	border-top-width: 0;
	display: inline-block;
	vertical-align: middle;
}

.accordion-item .collapsed .accordion-arrow {
	border-top: 5px solid #666d70;
	border-bottom-width: 0;
}

.accordion-item .accordion-no-js {
	display: block;
}

/* specfic case for accordion in a modal */

.modal .accordion-item .collapse {
	display: none;
}

.modal .accordion-item .collapse.in {
	display: block;
}

.accordion-item a.accordion-no-js {
	pointer-events: none;
	cursor: default;
}

/* Action links */

.action-link-container {
}

.action-link-container .action-group {
	margin: 0;
	padding: 0;
}

.action-link-container .action-group-item {
	line-height: 20px;
	font-family: 'Gotham bold', Arial, sans-serif;
	white-space: nowrap;
}

.action-link-container .action-group-item:last-child {
}

.action-link-container a {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.action-group-item {
	display: block;
	margin-right: 5px;
	position: relative;
}

.action-group-item .button-bg-gray {
	border: 1px solid #d1d3d4;
	color: #00334c;
}

.action-group-item .button-bg-blue {
	border: 1px solid #6571b3;
}

.action-group-item a {
	color: #ffffff;
	text-decoration: none;
}

.action-group-item.coloured-blocks a:focus .row {
	outline: 3px solid #d10074;
}

.action-group-item .button-bg-blue:hover {
	background-color: #00838f !important;
	border: 1px solid #d1d3d4;
	color: #ffffff;
	opacity: 1;
}

.action-group-item .button-bg-gray:hover {
	background-color: #00838f !important;
	color: #ffffff !important;
	opacity: 1;
}

.action-group-item .button-bg-gray a:hover {
	color: #ffffff !important;
	text-decoration: none;
}

.action-link-container ul li {
	margin-bottom: 5px;
}

@media screen and (min-width: 768px) {
	.action-link-container .action-group {
		display: table;
	}
	.action-link-container .action-group-item {
		vertical-align: top;
	}
	.action-link-container .action-group-item:not(:last-child) {
		border-right-width: 0 !important;
	}
	.action-link-container {
		width: 100%;
		text-align: right;
		border-left: 0;
	}
	.flexbox .action-link-container .action-group {
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
	}
	.flexbox .action-link-container .action-group.call-to-action .action-group-item {
		padding: 0;
	}
	.flexbox .action-link-container .action-group-item {
		display: block;
		line-height: 20px;
		height: 42px;
	}
	.flexbox .action-link-container {
		width: auto;
		-webkit-flex-grow: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
	}
	.btn .btn-primary .btn-lg .btn-block .button-bg-blue a:hover {
		color: #ffffff;
	}
	.btn .btn-primary .btn-lg .btn-block .button-bg-blue a:focus {
		color: #ffffff;
	}
}

.spacer20 {
	margin-bottom: 20px;
}

/* Employ confirmations results now has 4 columns */

.main-employer-results .practitioner table th,
.main-employer-results .practitioner table td {
	width: 25%;
}

.employer-spacing {
	margin-bottom: 15px;
}

/* Hide menu items */

@media screen and (min-width: 992px) {
	.utility-mobile {
		display: none;
		visibility: hidden;
	}
}

/* Additon of PPT icon*/

.ppt-link {
	font-family: 'Gotham bold', Arial, sans-serif;
}

.ppt-link:before {
	display: inline-block;
	vertical-align: top;
	font-size: inherit;
	width: 1em;
	content: '';
	margin-right: 4px;
	margin-left: 2px;
	background-size: 100% auto;
	background-repeat: no-repeat;
	background-position: center center;
}

.ppt-link:before {
	background-image: url('/static/img/files/ppt.svg');
}

.no-svg .ppt-link:before {
	background-image: url('/static/img/files/ppt.png');
}

/* Search register info alert text */

.prose.alert.alert-info {
	color: #000000;
	border: 0;
	border-left: 16px solid #d72882;
	background: #fbe9f2;
	padding: 35px 30px;
}

.prose.alert.alert-info a {
	color: #111111 !important;
}

.prose.alert.alert-info p strong {
	font-weight: 400;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.prose.alert.alert-info a {
	color: #ce0271;
}

#ie10 {
	display: none;
}

.ltie10 {
	display: none;
}

.alert.alert-info p {
	font-size: 16px;
}

.dismiss-alert a {
	font-size: 20px;
	color: #000000;
	padding-top: 0px;
}

.main-practitioner .practitioner table th:last-child,
.main-practitioner .practitioner table td:last-child {
	width: 26%;
}

.practitioner-search table th:last-child,
.practitioner-search table td:last-child {
	width: 6.2%;
}

.practitioner .panel-group .table-responsive {
	margin-top: 0px;
}

.accordion-body .open-text a {
	white-space: initial;
}

.remove-icon {
	display: inline;
	padding-right: 5px;
	height: 15px;
	margin-bottom: 2px;
}

.clear-file-upload {
	margin-left: 20px;
	line-height: 45px;
}

@media screen and (max-width: 500px) {
	.clear-file-upload {
		margin-left: 10px;
	}
}

.main-content .upload-text {
	display: none;
	visibility: hidden;
}

.row.no-padding > [class*='col-'] {
	padding: 0;
	margin: 0;
}

@media screen and (max-width: 767px) {
	.hide-mobile {
		display: none !important;
		visibility: hidden !important;
	}
}

@media screen and (max-width: 991px) {
	.hide-tablet {
		display: none !important;
		visibility: hidden !important;
	}
}

@media screen and (min-width: 992px) {
	.hide-desktop {
		display: none !important;
		visibility: hidden !important;
	}
}

/*Index*/

.hub-index section.container {
	padding-top: 20px;
	padding-bottom: 20px;
}

.hub-index .banner-image {
	padding-bottom: 220px;
}

.hub-index h1.banner-title {
	max-width: 100%;
}

.hub-index .row.intro {
	margin-bottom: 30px;
}

.hub-index .row.no-padding:not(.tiles) > [class*='col-'] {
	padding: 0 15px;
}

/*tablet up styling*/

@media screen and (min-width: 768px) {
	.hub-index .banner-image {
		padding-bottom: 445px;
	}
	.hub-index section.container {
		padding-top: 25px;
		padding-bottom: 5px;
	}
	.hub-index .row.no-padding {
		margin: 0 -15px 30px -15px;
	}
	.hub-index .row.no-padding:not(.tiles) > [class*='col-'] {
		padding: 0 5px;
	}
}

/*Desktop styling*/

@media screen and (min-width: 992px) {
	.hub-index .row.no-padding {
		padding: 0 10px;
	}
	.hub-index .banner-image {
		height: 200px;
		padding-bottom: 10%;
	}
	.hub-index section.container {
		padding-top: 40px;
	}
	.hub-index .row.intro {
		margin-bottom: 35px;
	}
	.hub-index .row.no-padding {
		margin: 0 auto 35px auto;
	}
}

/*this css is temporary*/

.banner-image {
	background-image: url('/static/tmp/banner.jpg');
}

@media screen and (max-width: 768px) {
	.banner-image {
		background-image: url('/static/tmp/banner--mobile.jpg');
	}
	.policy-listings {
		padding-top: 15px;
	}
	.prose a.tile {
		text-decoration: none;
	}
}

/*Tiles*/
.tiles .tile {
	background: #495ad4;
}

.tile {
	height: 110px;
	margin: 2px 0;
	padding: 25px 20px;
	display: flex;
	align-items: center;
}

.tile .info {
	display: flex;
	align-items: center;
	width: 100%;
	color: #fff;
}

.tile .tile-icon {
	height: 40px;
	margin-left: auto;
	width: 40px;
	background: url('/static/img/refresh/arrow-right-white.svg') no-repeat;
	background-size: contain;
	display: block;
	margin-top: auto;
}

.tile h4 {
	margin: 0;
	font-size: 22px;
	color: #fff;
	max-width: 220px;
}
.tile p {
	color: #fff;
	font-size: 22px;
	margin: 0;
}
/* 
.tile:hover {
	text-decoration: none;
	background: #7adfc3;
}
.tile:hover h4,
.tile:hover p {
	color: #111111;
}
.tile:hover .tile-icon {
	background: url('/static/img/refresh/arrow-right.svg') no-repeat;
} */

.tile .icon {
	display: inline-block;
}

.tile img {
	width: 50%;
}

.category-box-Black {
	background-color: #111111;
}

.category-box-Green {
	background-color: #438516;
}

.category-box-Grey {
	background-color: #666d70;
}

.category-box-Blue {
	background-color: #0096bf;
}

.category-box-Orange {
	background-color: #b44113;
}

.category-box-LightBlue {
	background-color: #00838f;
}

.category-box-Lavender {
	background-color: #6571b3;
}

.category-box-DarkBlue {
	background-color: #00334c;
}

.category-box-Pink {
	background-color: #d10074;
}

/*tablet up styling*/

@media screen and (min-width: 768px) {
	.tile {
		margin: 5px;
		height: 245px;
		padding: 30px 25px;
		display: block;
	}
	.tile .info {
		position: absolute;
		bottom: 30px;
		width: calc(100% - 55px);
	}
}

/*Tablet up*/

@media screen and (min-width: 768px) {
	.two-column-block {
		padding-top: 40px;
	}
}

.two-column-block h4 {
	margin-bottom: 30px;
}

.two-column-block .recent-document {
	position: relative;
	margin-bottom: 20px;
}

.two-column-block .recent-document:last-child {
	margin-bottom: 0;
}

.two-column-block .recent-document svg {
	position: absolute;
	width: 10px;
	height: 10px;
	fill: #d10074;
	top: 7px;
}

.two-column-block .recent-document a {
	font-size: 18px;
	color: #00334c;
	font-family: 'Gotham bold', Arial, sans-serif;
	padding-left: 20px;
	display: block;
}

.two-column-block .recent-document span {
	font-size: 18px;
	color: #666d70;
	padding-left: 20px;
}

.two-column-block .btn {
	font-weight: 400;
	font-size: 14px;
	padding: 10px 20px;
	margin-top: 10px;
}

.table-row-gray {
	background-color: #eff0f0;
}

/*desktop*/

@media screen and (min-width: 992px) {
	.two-column-block {
		padding-bottom: 70px;
	}
	.main-content .row.no-padding.tiles {
		margin: 0 auto 35px auto;
	}
}

@media all and (max-width: 768px) {
	.summary {
		width: 310px;
	}
}

@media all and (min-width: 769px) {
	.summary {
		width: 100%;
	}
}

.largertypography .btn-ghost svg {
	margin-right: 10px;
	position: relative;
	top: -1px;
}

.largertypography .accordion.result-page .single.warning {
	background: #fffbcc;
}

.largertypography .accordion.result-page .single.warning:hover,
.largertypography .accordion.result-page .single.warning.active {
	background: #fffbcc;
}

.largertypography .accordion.result-page .single.warning .ac-toggle:before {
	content: '';
	width: 25px;
	height: 25px;
	top: 16px;
	color: #b44113;
	background: url('/static/img/info-warning.svg') center center no-repeat;
	background-size: 25px 25px;
}

div.content-wrapper.largertypography div.highlight p.NoteCalloutNumbers {
	font-size: 48px;
	margin-top: -30px;
	margin-bottom: 10px;
	/*font-weight: bold;*/
	font-weight: normal;
}

.largertypography .accordion.result-page .single .ac-toggle.ac-toggle-disabled::after {
	background: none;
}

a.ac-toggle-disabled {
	pointer-events: none;
	cursor: default;
}

.main.largertypography h2.media-heading {
	color: #111111;
}

main.largertypography h2.media-heading a {
	/*color: black !important;*/
}

div.cta-bar a.btn.btn-lg img.no-bottom-margin {
	margin-bottom: 0px;
	padding-bottom: 0px;
}

.container-form .container .Form__Element--NonData {
	margin-bottom: 25px;
}

.container-form .container .Form__Element--NonData .external-link {
	text-decoration: underline;
	color: #d10074;
}

.largertypography .btn a {
	white-space: normal;
}

/* only hide the captcha badge for ""email a friend"" as it is misplaced on some pages */
#email-colleague-modal .grecaptcha-badge {
	visibility: hidden;
}

.full-width-landing-mainbody {
	margin-top: -10px;
	margin-bottom: 40px;
}

.prose table ul li,
table ol li,
.table td p {
	font-size: 14px;
	margin-bottom: 0px;
}

.footer-secondary a {
	text-decoration: underline;
}

.footer-secondary a:hover,
.footer-secondary a:focus {
	text-decoration: none;
}

/* Helpers
######################################################################*/

/**
 * Flag to determine if js only class
  */

.js-only {
	display: none;
	visibility: hidden;
}

.hide {
	display: none;
	visibility: hidden;
}

.show {
	visibility: visible;
}

body.white-bg {
	background-color: #ffffff;
}

/* Dont add anything below Helpers accept for accessability*/

.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
	position: static;
	width: auto;
	height: auto;
	overflow: visible;
	clip: auto;
	white-space: normal;
}

/* *:focus {
	outline: 3px solid #d10074 !important;
	outline-offset: -1px !important;
} */

.grecaptcha-badge {
	visibility: hidden;
}

/* Skip navigation link */

.skip-to-content-link {
	background: #d10074;
	color: #fff !important;
	font-weight: 700;
	left: 10px;
	padding: 5px 10px;
	z-index: 102;
	position: absolute;
	top: -300px;
	z-index: 9990;
}

@media all and (min-width: 768px) {
	.skip-to-content-link:focus {
		top: 10px;
	}
}

/* Default select menu  */

select option:disabled {
	color: #000000 !important;
}

a:focus {
	color: inherit;
}

@charset "UTF-8";
.registration {
	background: red;
}

.largertypography .content-wrapper {
	padding: 30px 0;
	/*border-top: 2px solid #d8d8d8;*/
}

.largertypography .content-wrapper.with-border {
	border-top: 2px solid #d8d8d8;
}

.largertypography .content-wrapper hr {
	margin-top: 25px;
	margin-bottom: 25px;
	border-top: 2px solid #d8d8d8;
}

@media screen and (min-width: 991px) {
	.largertypography .content-wrapper hr {
		margin-top: 35px;
		margin-bottom: 35px;
	}
}

.largertypography .content-wrapper.no-border {
	padding-top: 0;
	border-top: 0;
}

.largertypography .content-wrapper.small-padding {
	padding: 15px 0;
}

.largertypography .btn-jumbo {
	height: 50px;
	padding: 15px 20px;
}

.largertypography .primary-link {
	font-size: 18px;
	position: relative;
	color: #d10074;
	font-weight: bold;
	padding-left: 45px;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	display: block;
}

/* .largertypography .primary-link:before {
	content: '';
	position: absolute;
	left: 0;
	top: -7px;
	width: 34px;
	height: 34px;
	background: #d10074;
	border-radius: 50%;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
} */

.largertypography .primary-link:after {
	content: '';
	position: absolute;
	left: 0;
	top: 15px;
	width: 35px;
	height: 35px;
	background: url('/static/img/refresh/arrow-right.svg') center center no-repeat;
	background-size: contain;
	transform: translateY(-50%);
}

.largertypography .primary-link:hover,
.largertypography .primary-link.hover {
	text-decoration: underline;
}

.largertypography .primary-link:hover:before,
.largertypography .primary-link.hover:before {
	background: #00334c !important;
}

.largertypography .primary-link:visited,
.largertypography .primary-link.visited {
	text-decoration: none;
	color: #d10074;
}

.largertypography .primary-link:visited:before,
.largertypography .primary-link.visited:before {
	background: #d10074;
}

.largertypography .list-link {
	font-size: 18px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	border-bottom: 1px solid #00334c;
	color: #00334c;
}

.largertypography p a {
	color: #d10074;
	-webkit-transition: color 100ms ease;
	transition: color 100ms ease;
}

.largertypography .list-link:hover,
.largertypography .list-link.hover {
	text-decoration: none;
	color: #d10074;
	border-color: #d10074;
}

.largertypography .list-link:visited,
.largertypography .list-link.visited {
	text-decoration: none;
	color: #d10074;
	/*#331272;*/
	border-color: #331272;
}

.largertypography .paragraph-link {
	font-size: 18px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	color: #d10074;
	text-decoration: underline;
}

.largertypography .paragraph-link:hover,
.largertypography .paragraph-link.hover {
	color: #00334c;
}

.largertypography .paragraph-link:visited,
.largertypography .paragraph-link.visited {
	color: #d10074;
	/*#331272;*/
}

.largertypography .fz-18 {
	font-size: 18px;
}

.largertypography .title {
	padding-bottom: 15px;
}

@media screen and (min-width: 991px) {
	.largertypography .title {
		font-size: 48px;
	}
}

.largertypography .fw-bold {
	font-weight: bold;
}

.largertypography .dark-color {
	color: #111111;
}

.largertypography .text-underline {
	display: inline-block;
	font-weight: normal;
}

.largertypography .result-title {
	padding-bottom: 0px;
	margin-top: 0 !important;
}

.largertypography .result-page-lead {
	margin-bottom: 30px !important;
	padding-bottom: 0 !important;
}

.largertypography .external-link,
.largertypography .go-back {
	position: relative;
	padding-left: 20px;
	margin-bottom: 30px;
}

.largertypography a.external-link {
	padding-left: 0;
}

.largertypography .external-link a,
.largertypography .go-back a {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: cneter;
	-ms-flex-align: cneter;
	align-items: cneter;
	font-size: 18px;
	/*font-weight: bold; NES*/

	font-weight: normal;
}

.largertypography .external-link a span,
.largertypography .go-back a span {
	text-decoration: underline;
}

.largertypography .external-link a img,
.largertypography .go-back a img {
	margin-left: 10px;
}

.largertypography .external-link a:hover,
.largertypography .go-back a:hover {
	text-decoration: none;
}

.largertypography .external-link {
	font-size: 18px !important;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

.largertypography .external-link.secondary {
	padding-right: 20px;
}

.largertypography .external-link.secondary:before {
	position: absolute;
	right: 0;
	top: 0;
	background: url('/static/img/refreshexternal-link.svg') center center no-repeat;
	background-size: 15px 15px;
	margin: 0;
}

@media screen and (min-width: 768px) {
	.largertypography .external-link.secondary {
		padding-left: 20px;
		padding-right: 30px;
	}
	.largertypography .external-link.secondary:before {
		background-size: 20px 20px;
		top: -2px;
	}
}

.largertypography .go-back {
	padding-left: 0;
	padding-top: 0px;
}

.largertypography .go-back.with-border {
	border-top: 2px solid #d8d8d8;
	padding-top: 40px;
	display: none;
}

.largertypography .go-back a {
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	font-size: 14px;
}

.largertypography .go-back a img {
	margin-right: 10px;
	margin-left: 0;
	position: relative;
	top: 3px;
}

@media screen and (min-width: 991px) {
	.largertypography .go-back {
		padding-top: 20px;
	}
	.largertypography .go-back.with-border {
		display: block;
	}
	.largertypography .go-back a {
		font-size: 18px;
	}
	.largertypography .go-back a img {
		top: 6px;
	}
}

.largertypography .share {
	padding-top: 6px !important;
}

@media screen and (min-width: 991px) {
	.largertypography .share {
		padding-top: 15px !important;
	}
}

.largertypography .share .list-group li {
	border: 0;
	padding-left: 0;
	width: auto;
	display: inline-block;
}

.largertypography .share .list-group li:last-child {
	padding-right: 0;
}

@media screen and (min-width: 768px) {
	.largertypography .share .list-group li:last-child {
		border-top: 0;
	}
}

.largertypography .with-icon {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	padding-right: 70px;
	position: relative;
}

.largertypography .with-icon.no-border:before {
	border-left: 0;
}

.largertypography .with-icon.hover {
	opacity: 0.8;
}

.largertypography .with-icon.loading {
	opacity: 0.8;
}

.largertypography .with-icon.loading:before {
	background: url('/static/img/loading.svg') center center no-repeat;
	background-size: 12px 12px;
	-webkit-animation: rotate 1s infinite linear;
	animation: rotate 1s infinite linear;
}

.largertypography .with-icon:before {
	content: '';
	position: absolute;
	right: 0;
	top: 0;
	width: 40px;
	height: 100%;
	border-left: 2px solid rgba(255, 255, 255, 0.4);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	background: url('/static/img/arrow-left-white.svg') center center no-repeat;
	background-size: 16px 12px;
}

.largertypography .with-icon.btn-white:before {
	left: 0;
	right: auto;
	background: url('/static/img/arrow-right.svg') center center no-repeat;
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
}

.largertypography .with-icon.btn-white.loading:before {
	background: url('/static/img/loading-primary.svg') center center no-repeat;
}

.largertypography .btn-white {
	background: #fff;
	border: 1px solid #d1d3d4;
	padding-left: 40px;
	padding-right: 40px;
}

.largertypography .btn-collapse {
	position: relative;
	z-index: 2;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	padding-left: 20px;
	padding-right: 20px;
	background: #d10074;
	height: 40px;
	color: #fff;
}

.largertypography .btn-collapse.showLink img {
	-webkit-transform: scale(-1);
	-ms-transform: scale(-1);
	transform: scale(-1);
}

.largertypography .btn-collapse img {
	width: 10px;
	margin-right: 10px;
}

.largertypography .btn-collapse.rounded {
	border-radius: 30px;
}

.largertypography .btn-bg-green {
	background: #331272 !important;
}

.largertypography .tittle-with-border {
	padding: 20px 0;
	display: block;
	border-top: 2px solid #d8d8d8;
	margin: 0;
}

.largertypography .blueprint .row {
	max-width: 1140px;
}

.largertypography .components > h2 {
	margin-bottom: 30px;
}

.largertypography .components .mt-50 {
	margin-top: 50px;
}

.largertypography .components .component-title {
	font-size: 12px;
	text-transform: uppercase;
	color: #666d70;
	display: block;
	margin-bottom: 10px;
	margin-top: 10px;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

@-webkit-keyframes rotate {
	from {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes rotate {
	from {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

.largertypography h1 {
	font-size: 28px;
	margin-bottom: 25px;
}

.largertypography h2 {
	font-size: 24px;
}

.largertypography h3 {
	font-size: 21px;
}

.largertypography h4 {
	font-size: 21px;
}

.largertypography h5 {
	font-size: 18px;
}

.largertypography h6 {
	font-size: 16px;
}

.largertypography .text-block h2:first-child,
.largertypography .content-wrapper h2:first-child,
.largertypography .text-block h3:first-child,
.largertypography .content-wrapper h3:first-child {
	margin-top: 0px;
}

@media screen and (min-width: 576px) {
	.largertypography h1 {
		font-size: 48px;
	}
	.largertypography h2 {
		font-size: 28px;
	}
	.largertypography h3 {
		font-size: 24px;
	}
}

.largertypography .lead {
	margin-bottom: 30px;
	/*margin-left: 15px; */
	/* NES */
}

@media screen and (min-width: 992px) {
	.largertypography .lead {
		margin-bottom: 40px;
	}
}

.largertypography .lead.bold {
	/*font-weight: bold;
  color: #666d70;    WG*/
	font-weight: normal;
	color: #666d70;
	margin-top: -15px;
}

@media (max-width: 576px) {
	.largertypography .lead:not(.sub-title) {
		font-size: 18px;
		margin-bottom: 25px;
		line-height: 27px;
	}
}

/* Not needed
.largertypography a:not(.btn):hover {
  color: #00334c !important;
}
*/

.largertypography.result-page .nmc-register-responsive.video-embed {
	margin-bottom: 0;
}

@media (max-width: 768px) {
	.largertypography.result-page .external-link.secondary {
		padding-left: 25px;
		display: block;
	}
	.largertypography.result-page .external-link.secondary span {
		font-size: 16px;
	}
	.largertypography.result-page .external-link.secondary:before {
		position: absolute;
		left: 0;
	}
}

.largertypography.result-page .content-wrapper {
	margin-bottom: 30px;
}

.largertypography.result-page .content-wrapper.small-padding {
	margin-bottom: 0;
}

.largertypography.result-page .content-wrapper.small-padding .with-icon {
	margin-bottom: 0;
}

@media (max-width: 576px) {
	.largertypography.result-page .content-wrapper.small-padding .with-icon {
		width: 100%;
	}
}

@media screen and (min-width: 991px) {
	.largertypography.result-page .content-wrapper {
		margin-bottom: 50px;
	}
}

@media screen and (min-width: 991px) {
	.largertypography.result-page .highlight {
		margin-bottom: 40px;
	}
}

.largertypography .result-pdf-page .content-wrapper {
	border-width: 4px;
	margin-bottom: 30px;
}

.largertypography.content-wrapper {
	padding: 30px 0;
	border-top: 2px solid #d8d8d8;
}

.largertypography.content-wrapper hr {
	margin-top: 10px;
	margin-bottom: 25px;
	border-top: 2px solid #d8d8d8;
}

@media screen and (min-width: 991px) {
	.largertypography.content-wrapper hr {
		margin-top: 10px;
		margin-bottom: 35px;
	}
}

.largertypography.content-wrapper.no-border {
	padding-top: 0;
	border-top: 0;
}

.largertypography.content-wrapper.small-padding {
	padding: 15px 0;
}

.components > h2 {
	margin-bottom: 30px;
}

.components hr {
	margin-top: 25px;
	margin-bottom: 25px;
	border-top: 2px solid #d8d8d8;
}

@media screen and (min-width: 991px) {
	.components hr {
		margin-top: 35px;
		margin-bottom: 35px;
	}
}

.components hr .mt-50 {
	margin-top: 50px;
}

.components hr .component-title {
	font-size: 12px;
	text-transform: uppercase;
	color: #666d70;
	display: block;
	margin-bottom: 10px;
	margin-top: 10px;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

.components .largertypography.primary-link {
	font-size: 21px;
	position: relative;
	color: #d10074;
	/*font-weight: bold; NES*/
	font-weight: normal;
	padding-left: 45px;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.components .largertypography.primary-link:before {
	content: '';
	position: absolute;
	left: 0;
	top: -7px;
	width: 34px;
	height: 34px;
	background: #d10074;
	border-radius: 50%;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.components .largertypography.primary-link:after {
	content: '';
	position: absolute;
	left: 0;
	top: -1px;
	width: 27px;
	height: 22px;
	background: url('/static/img/primary-link-arrow.svg') center center no-repeat;
	background-size: 27px 22px;
}

.components .largertypography.primary-link:hover,
.components .largertypography.primary-link.hover {
	text-decoration: none;
	color: #00334c !important;
}

.components .largertypography.primary-link:hover:before,
.components .largertypography.primary-link.hover:before {
	background: #00334c !important;
}

.components .largertypography.primary-link:visited,
.components .largertypography.primary-link.visited {
	text-decoration: none;
	color: #d10074;
	/*#331272;*/
}

.components .largertypography.primary-link:visited:before,
.components .largertypography.primary-link.visited:before {
	background: #331272;
}

.largertypography.with-icon {
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	padding-right: 70px;
	position: relative;
}

.largertypography.with-icon.no-border:before {
	border-left: 0;
}

.largertypography.with-icon.hover {
	opacity: 0.8;
}

.largertypography.with-icon.loading {
	opacity: 0.8;
}

.largertypography.with-icon.loading:before {
	background: url('/static/img/loading.svg') center center no-repeat;
	background-size: 12px 12px;
	-webkit-animation: rotate 1s infinite linear;
	animation: rotate 1s infinite linear;
}

.largertypography.with-icon:before {
	content: '';
	position: absolute;
	right: 0;
	top: 0;
	width: 40px;
	height: 100%;
	border-left: 2px solid rgba(255, 255, 255, 0.4);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	background: url('/static/img/arrow-left-white.svg') center center no-repeat;
	background-size: 16px 12px;
}

.largertypography.with-icon.btn-white:before {
	left: 0;
	right: auto;
	background: url('/static/img/arrow-right.svg') center center no-repeat;
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
}

.largertypography.with-icon.btn-white.loading:before {
	background: url('/static/img/loading-primary.svg') center center no-repeat;
}

.largertypography.list-link {
	font-size: 18px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	border-bottom: 1px solid #00334c;
	color: #00334c;
}

.largertypography.list-link:hover,
.largertypography.list-link.hover {
	text-decoration: none;
	color: #d10074;
	border-color: #d10074;
}

.largertypography.list-link:visited,
.largertypography.list-link.visited {
	text-decoration: none;
	color: #d10074;
	/*#331272;*/
	border-color: #331272;
}

.largertypography.paragraph-link {
	font-size: 18px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	color: #d10074;
	text-decoration: underline;
}

.largertypography.paragraph-link:hover,
.largertypography.paragraph-link.hover {
	color: #00334c;
}

.largertypography.paragraph-link:visited,
.largertypography.paragraph-link.visited {
	color: #d10074;
	/*#331272;*/
}

.largertypography.btn-white {
	background: #fff;
	border: 1px solid #d1d3d4;
	padding-left: 40px;
	padding-right: 40px;
}

.largertypography.btn-collapse {
	position: relative;
	z-index: 2;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	padding-left: 20px;
	padding-right: 20px;
	background: #d10074;
	height: 40px;
	color: #fff;
}

.largertypography.btn-collapse.active img {
	-webkit-transform: scale(-1);
	-ms-transform: scale(-1);
	transform: scale(-1);
}

.largertypography.btn-collapse img {
	width: 10px;
	margin-right: 10px;
}

.largertypography.btn-collapse.rounded {
	border-radius: 30px;
}

.largertypography.share {
	padding-top: 6px !important;
	padding-bottom: 6px !important;
}

@media screen and (min-width: 991px) {
	.largertypography.share {
		padding-top: 15px !important;
		padding-bottom: 15px !important;
	}
}

.largertypography.share .list-group li {
	border: 0;
	padding-left: 0;
	width: auto;
	display: inline-block;
}

.largertypography.share .list-group li.share-updated {
	width: 100%;
}

.largertypography.share .list-group li:last-child {
	padding-right: 0;
	border-top: 1px solid #d1d3d4;
}

@media screen and (min-width: 768px) {
	.largertypography.share .list-group li:last-child {
		border-top: 0;
	}
}

body {
	overflow: auto;
}

body.modal-open {
	position: relative;
	overflow: hidden;
}

@media print {
	.largertypography .primary-link {
		padding-left: 0;
	}
	.largertypography .primary-link:before {
		display: none;
	}
	.largertypography .primary-link:after {
		content: '(' attr(href) ')';
		color: #000 !important;
		text-decoration: none !important;
		font-size: 14px;
		word-wrap: break-word;
		word-break: break-word;
		position: relative;
		top: auto;
		left: auto;
		width: auto;
		height: auto;
		background: none;
	}
	.largertypography .go-back.with-border {
		display: block !important;
		border: 0 !important;
	}
	.largertypography .content-wrapper hr {
		display: block !important;
		margin-top: 20px !important;
		margin-bottom: 20px !important;
	}
	.largertypography .external-link.secondary {
		padding-left: 0;
	}
}

.largertypography .breadcrumb {
	display: none;
}

@media screen and (min-width: 992px) {
	.largertypography .breadcrumb {
		display: block;
	}
}

.largertypography .sub-title {
	margin-bottom: 30px;
}

@media screen and (min-width: 991px) {
	.largertypography .sub-title {
		margin-bottom: 45px;
	}
}

.largertypography .sub-title.bold {
	/*font-weight: bold; NES*/
	font-weight: normal;
	color: #666d70;
}

.largertypography .inner-title {
	font-size: 18px;
	/*font-weight: bold;  WG*/
	padding-bottom: 10px;
}

.largertypography .inner-title.mt-0 {
	margin-top: 0;
	padding-bottom: 0;
}

.largertypography .banner-title {
	max-width: 80%;
}

.largertypography .two-column-large-card {
	margin-bottom: 30px;
}

/* added spacing for multiple rows*/

.largertypography .two-column-large-card .row {
	margin-bottom: 0;
}

@media screen and (min-width: 768px) {
	.largertypography .two-column-large-card {
		margin-bottom: 50px;
	}
	/* added spacing for multiple rows*/
	.largertypography .two-column-large-card .row {
		margin-bottom: -2px;
		/* NES was 30px*/
	}
}

@media screen and (max-width: 768px) {
	.largertypography .two-column-large-card .row {
		margin-bottom: -2px;
		/* NES was 30px*/
	}
}

.largertypography .two-column-large-card .how-to-register {
	margin-bottom: 0;
}

.largertypography .two-column-large-card .row .col-lg-6,
.largertypography .two-column-large-card .row .col-lg-4 {
	margin-bottom: 0;
}

.largertypography .two-column-large-card .row .col-lg-6:last-child .how-to-register,
.largertypography .two-column-large-card .row .col-lg-4:last-child .how-to-register {
	border-top: 0;
}

@media screen and (min-width: 991px) {
	.largertypography .two-column-large-card .row {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: stretch;
		-ms-flex-align: stretch;
		align-items: stretch;
		-webkit-box-flex: 1;
		-ms-flex: 1;
		flex: 1;
	}
	.largertypography .two-column-large-card .row .col-lg-6,
	.largertypography .two-column-large-card .row .col-lg-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		-webkit-box-flex: 1;
		-ms-flex: 1;
		flex: 1;
	}
	.largertypography .two-column-large-card .row .col-lg-6 .how-to-register,
	.largertypography .two-column-large-card .row .col-lg-4 .how-to-register {
		height: 100%;
		margin: 0;
	}
	.largertypography .two-column-large-card .row .col-lg-6 .how-to-register .media,
	.largertypography .two-column-large-card .row .col-lg-4 .how-to-register .media {
		margin-bottom: 0;
		height: 100%;
	}
}

.largertypography .how-to-register {
	margin-bottom: 30px;
	margin-left: -30px;
	margin-right: -30px;
	border-width: 2px;
}

.largertypography .how-to-register .media {
	border-width: 2px;
}

.largertypography .how-to-register.no-image .media-body .media-body h1.media-heading {
	font-size: 28px;
}

.largertypography .how-to-register.grid-layout .media > img {
	max-width: 100%;
	width: 100%;
}
.largertypography .how-to-register.grid-layout .media .media-body {
	padding-left: 10px;
}

@media (min-width: 992px) {
	.largertypography .how-to-register.no-image .media {
		border-top-width: 4px;
	}

	.largertypography .how-to-register.grid-layout .media .media-body {
		padding-left: 0;
	}
	.largertypography .how-to-register.grid-layout .media .media-body .media-heading,
	.largertypography .how-to-register.grid-layout .media .media-body p {
		padding-left: 0;
	}
	.largertypography .how-to-register.grid-layout .media .media-body .primary-link {
		margin-left: 0;
	}

	.largertypography .col-lg-6 .how-to-register.grid-layout .media .media-body {
		padding-left: 20px;
	}
}

.largertypography .how-to-register.grid-layout .media .media-body h1.media-heading {
	font-size: 28px;
}

.largertypography .how-to-register .media-object {
	display: none;
}

.largertypography .how-to-register .media-body {
	padding: 30px 20px;
}

.largertypography .how-to-register .media-body p {
	margin-bottom: 20px;
}

.largertypography .how-to-register .media-body .media-heading {
	padding-bottom: 10px;
}

.largertypography .how-to-register .media-body .media-heading:before {
	display: none;
}

.largertypography .how-to-register .media-body h1.media-heading {
	font-size: 28px;
}

.largertypography .how-to-register .media-body h2.media-heading {
	font-size: 28px;
}

.largertypography .how-to-register .media-body h3.media-heading {
	font-size: 24px;
}

.largertypography .how-to-register .media-body h4.media-heading {
	font-size: 20px;
}

.largertypography .how-to-register .media-body h5.media-heading,
.largertypography .how-to-register .media-body h6.media-heading {
	font-size: 18px;
}

@media screen and (min-width: 992px) {
	.largertypography .how-to-register {
		margin-left: 0;
		margin-right: 0;
		border-width: 1px;
		margin-bottom: 55px;
	}
	.largertypography .how-to-register .media {
		border-width: 1px;
	}
	.largertypography .how-to-register .media-body p {
		max-width: 388px;
	}
}

@media screen and (min-width: 768px) {
	.largertypography .how-to-register .media-body h1.media-heading {
		font-size: 34px;
	}
	.largertypography .how-to-register .media-body h1.media-heading a {
		color: #111111 !important;
	}
	.largertypography .how-to-register .media-body h1.media-heading {
		font-size: 36px;
	}
	.largertypography .how-to-register .media-body h2.media-heading {
		font-size: 34px;
	}
	.largertypography .how-to-register .media-body h3.media-heading {
		font-size: 28px;
	}
	.largertypography .how-to-register .media-body h4.media-heading {
		font-size: 24px;
	}
	.largertypography .how-to-register .media-body h5.media-heading,
	.largertypography .how-to-register .media-body h6.media-heading {
		font-size: 20px;
	}
	.largertypography .how-to-register .media-body p {
		font-size: 21px;
	}
	.largertypography .how-to-register .media-object {
		display: block;
		width: 446px;
		max-width: 446px;
	}
}

.learn-more {
	margin-bottom: 30px;
	position: relative;
}

.learn-more > h2 {
	margin-bottom: 30px;
}

@media (max-width: 576px) {
	.learn-more > h2 {
		font-size: 21px;
		margin-bottom: 20px;
	}
}

.learn-more .row .col-lg-6,
.learn-more .row .col-lg-4 {
	margin-bottom: 0px;
}

@media screen and (min-width: 991px) {
	.learn-more .row {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		-webkit-box-align: stretch;
		-ms-flex-align: stretch;
		align-items: stretch;
	}
	.learn-more .row .col-lg-6,
	.learn-more .row .col-lg-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		-webkit-box-flex: 1;
		-ms-flex: 1;
		flex: 1;
		margin-bottom: 15px;
	}
	.learn-more .row .col-lg-6 .media,
	.learn-more .row .col-lg-4 .media {
		height: 100%;
		margin: 0;
	}
}

.learn-more .media {
	padding-left: 20px;
	padding-right: 20px;
}

.learn-more .media .media-body {
	padding: 30px 20px 30px 0;
}

.learn-more .media .media-body p {
	display: none;
	line-height: 26px;
}

.learn-more .media-heading {
	position: relative;
	font-size: 18px;
	line-height: 29px;
	padding: 0 0 5px 20px;
}

.learn-more .media-heading:before {
	position: absolute;
	left: 0;
	top: 6px;
}

@media screen and (min-width: 991px) {
	.learn-more .media {
		border: 1px solid #d1d3d4;
		border-top-width: 4px;
	}
	.learn-more .media .media-body p {
		display: block;
	}
}

@media (max-width: 991px) {
	.learn-more {
		margin-bottom: 0;
	}
	.learn-more > .row:last-child .col-lg-6:last-child .media,
	.learn-more > .row:last-child .col-lg-4:last-child .media {
		border-bottom: 0;
	}
	.learn-more .media .media-body {
		padding-left: 0;
		padding-top: 15px;
		padding-bottom: 15px;
	}
	.learn-more .media .media-heading {
		font-size: 18px;
		margin: 0;
		padding-bottom: 0;
	}
}

@media print {
	.largertypography .siderbar-header {
		display: none;
	}
	.largertypography .learn-more .media-heading {
		padding-left: 0;
	}
	.largertypography .learn-more .media-heading:before {
		display: none;
	}
	.largertypography .learn-more .media .media-body {
		padding-left: 0;
		padding-bottom: 0;
	}
	.largertypography .learn-more .media .media-body p {
		display: block;
	}
	.largertypography .hub .media {
		border: 0 !important;
	}
	.largertypography .how-to-register {
		margin: 0 !important;
		margin-bottom: 40px !important;
		border: 1px solid #d8d8d8 !important;
		padding: 0px !important;
	}
	.largertypography .how-to-register .media-body {
		padding: 15px !important;
	}
	.largertypography .how-to-register .media-object {
		display: block !important;
		max-width: 250px !important;
	}
}

.largertypography .full-width-cta {
	position: relative;
	background-position: center center !important;
	background-repeat: no-repeat !important;
	background-size: cover !important;
}

.largertypography .full-width-cta .apply-body {
	position: relative;
	padding: 40px 0;
	overflow: hidden;
	text-align: center;
}

.largertypography .full-width-cta .apply-body img {
	width: 34px;
	margin: 0 auto;
}

.largertypography .full-width-cta .apply-body .questionmark {
	position: absolute;
	top: -5px;
	right: 0;
	background-size: cover !important;
	display: none;
}

.largertypography .full-width-cta .apply-body .questionmark.small {
	bottom: -15px;
	top: auto;
	right: auto;
	left: 0;
	width: 97px;
}

@media screen and (min-width: 768px) {
	.largertypography .full-width-cta .apply-body .questionmark {
		display: block;
	}
}

.largertypography .full-width-cta .apply-body h2 {
	color: #fff;
	margin-bottom: 25px;
}

@media screen and (min-width: 576px) {
	.largertypography .full-width-cta .apply-body h2 {
		font-size: 48px;
	}
}

.largertypography .full-width-cta .apply-body p {
	max-width: 550px;
	margin: 25px auto 30px;
	font-size: 21px;
	color: #fff;
}

@media screen and (min-width: 768px) {
	.largertypography .full-width-cta .apply-body {
		padding: 80px 0px;
	}
	.largertypography .full-width-cta .apply-body .h1 {
		font-size: 36px;
	}
}

.largertypography .btn-bg-white {
	background: #fff;
	color: #438516;
	border-radius: 3px;
}

@media print {
	.largertypography .full-width-cta {
		border: 1px solid #d8d8d8;
		background: none !important;
	}
	.largertypography .full-width-cta img {
		display: none !important;
	}
	.largertypography .full-width-cta .apply-body {
		padding: 15px;
		text-align: left;
	}
	.largertypography .full-width-cta .apply-body p {
		margin: 0;
	}
	.largertypography .full-width-cta .btn-primary {
		padding-left: 0;
	}
}

.larger-footer .footer-primary li a {
	position: relative;
}

@media screen and (min-width: 768px) {
	.larger-footer .footer-primary li a {
		padding-left: 20px;
	}
}

.larger-footer .footer-primary li a:before {
	position: absolute;
	right: 5px;
	top: 20px;
	content: '';
	width: 14px;
	height: 14px;
	background: 0 center url('/images/arrow-right.png') no-repeat;
	background-size: 10px 10px;
}

@media screen and (min-width: 991px) {
	.larger-footer .footer-primary li a:before {
		background: 0 center url('/images/arrow-right--magenta.png') no-repeat;
		vertical-align: baseline;
		display: inline-block;
		right: auto;
		left: 0;
		top: 13px;
	}
}

.largertypography .expand-lists {
	position: relative;
}

.largertypography .expand-lists.sidebar {
	margin-top: 15px;
	margin-bottom: 30px;
}

@media screen and (min-width: 768px) {
	.largertypography .expand-lists.sidebar .process-lists {
		padding-bottom: 20px;
		border-bottom: 2px solid #d8d8d8;
	}
}

.largertypography .expand-lists.sidebar .process-lists li {
	padding-bottom: 20px;
}

.largertypography .expand-lists.sidebar .process-lists li .count {
	-webkit-box-shadow: none;
	box-shadow: none;
	left: 1px;
}

.largertypography .expand-lists.sidebar .process-lists li .list-header h2 {
	font-size: 18px;
}

.largertypography .expand-lists.sidebar .process-lists li .list-wrapper {
	padding-bottom: 15px;
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content {
	padding-top: 15px;
}

.largertypography .expand-lists.sidebar .process-lists li.active .count {
	background: #111111;
	color: #fff;
}

.largertypography .expand-lists.sidebar .process-lists li.active .count.and {
	background: #fff;
	color: #111111;
}

.largertypography .expand-lists.sidebar .process-lists li.active .toggle-content .special-link:before {
	background: #111111;
}

.largertypography .expand-lists.sidebar .process-lists li.active:after {
	background: #111111;
}

.largertypography .expand-lists.sidebar .process-lists li:after {
	top: 30px;
}

.largertypography .expand-lists.sidebar .process-lists li:last-child:after {
	display: block;
	height: 0;
	top: 0px;
}

.largertypography .expand-lists.sidebar .process-lists li:last-child.list-expanded:after {
	top: 0px;
	height: calc(100% - 30px);
}

@media screen and (min-width: 768px) {
	.largertypography .expand-lists.sidebar .process-lists li:last-child {
		padding-bottom: 0;
		border-bottom: 0;
	}
	.largertypography .expand-lists.sidebar .process-lists li:last-child.list-expanded:after {
		top: 25px;
	}
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content {
	/*display: none;  NES Multistep Open if no javascript available*/
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content a,
.largertypography .expand-lists.sidebar .process-lists li .toggle-content p {
	font-size: 16px;
	padding-bottom: 0;
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content .special-link {
	position: relative;

	color: #111111;
	text-decoration: none;
	/* margin-top: 15px; NES Removed extra space above active item in multistep sidebar nav*/
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content .special-link span {
	color: #111111;
}

/*nes duplicate*/

.largertypography .expand-lists.sidebar .process-lists li a.toggle-content:visited .special-link {
	position: relative;
	/*font-weight: bold; NES*/
	font-weight: normal;
	color: #d10074;
	/*#111111;*/
	text-decoration: none;
	/* margin-top: 15px; NES Removed extra space above active item in multistep sidebar nav*/
}

.largertypography .expand-lists.sidebar .process-lists li .toggle-content .special-link:before {
	content: '';
	position: absolute;
	left: -44px;
	top: 2px;
	/* NES. was top: 0px*/
	width: 20px;
	height: 20px;
	border-radius: 50%;
	z-index: 8;
	background: #d8d8d8;
	border: 3px solid #fff;
}

.largertypography .expand-lists .trigger-process-list {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	margin-bottom: 15px;
}

@media screen and (min-width: 576px) {
	.largertypography .expand-lists .trigger-process-list {
		margin-bottom: 30px;
	}
}

.largertypography .expand-lists .trigger-process-list:before {
	position: absolute;
	content: '';
	width: 100%;
	height: 2px;
	top: 100%;
	left: 0;
	margin-top: -1px;
	background: #d8d8d8;
	z-index: 1;
}

.largertypography .expand-lists .trigger-process-list .btn {
	display: none;
}

.largertypography .expand-lists .trigger-process-list .show-mobile.active {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	padding-bottom: 10px;
	text-decoration: underline;
	font-size: 16px;
	color: #331272;
	color: #d10074;
	font-family: 'Gotham bold', Arial, sans-serif;
}
.largertypography .expand-lists .trigger-process-list .show-mobile.active:hover {
	text-decoration: none;
}

@media screen and (min-width: 768px) {
	.largertypography .expand-lists .trigger-process-list:not(.small) {
		margin-bottom: 0;
	}
	.largertypography .expand-lists .trigger-process-list:not(.small):before {
		width: calc(100% - 130px);
		top: 50%;
	}
	.largertypography .expand-lists .trigger-process-list:not(.small) .show-mobile {
		display: none;
	}
	.largertypography .expand-lists .trigger-process-list:not(.small) .btn-collapse {
		position: relative;
		z-index: 2;
		width: 125px;
		height: 40px;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		padding-left: 20px;
		padding-right: 20px;
	}
	.largertypography .expand-lists .trigger-process-list:not(.small) .btn-collapse img {
		width: 10px;
		margin-right: 10px;
	}
	.largertypography .expand-lists .trigger-process-list:not(.small) .btn-collapse.rounded {
		border-radius: 20px;
	}
}

.largertypography .expand-lists .process-lists {
	list-style: none;
	margin-top: 0;
	padding: 0;
}

.largertypography .expand-lists .process-lists li {
	padding-bottom: 15px;
	padding-left: 50px;
	position: relative;
}

@media screen and (min-width: 576px) {
	.largertypography .expand-lists .process-lists li {
		padding-bottom: 30px;
	}
}

.largertypography .expand-lists .process-lists li .count {
	width: 30px;
	height: 30px;
	line-height: 30px;
	border-radius: 50%;
	background: #d8d8d8;
	position: absolute;
	left: 0;
	text-align: center;
	font-weight: bold;
	z-index: 2;
	font-size: 18px;
}

.largertypography .expand-lists .process-lists li .count.and {
	background: #d8d8d8;
}

.largertypography .expand-lists .process-lists li:after {
	content: '';
	position: absolute;
	left: 14px;
	top: 30px;
	width: 4px;
	height: calc(100% - 30px);
	background: #d8d8d8;
	z-index: 1;
}

/*
.largertypography .expand-lists .process-lists li .list-header:hover h2 {
  color: #00334c;
}
*/

.largertypography .expand-lists .process-lists li .list-header h2 {
	cursor: pointer;
	font-size: 20px;
	margin-bottom: 5px;
	-webkit-transition: color 0.3s ease;
	-o-transition: color 0.3s ease;
	transition: color 0.3s ease;
	margin-top: 0;
}

.largertypography .expand-lists .process-lists li .list-header a {
	color: #331272;
	font-family: 'Gotham bold', Arial, sans-serif;
	font-size: 16px;
}

.largertypography .expand-lists .process-lists li .list-wrapper {
	border-bottom: 2px solid #d8d8d8;
	padding-bottom: 15px;
}

@media screen and (min-width: 576px) {
	.largertypography .expand-lists .process-lists li .list-wrapper {
		padding-bottom: 25px;
	}
}

.largertypography .expand-lists .process-lists li .toggle-content {
	/*display: none; NES MultiStep MultiStep Open if Javascript disabled*/
	padding-top: 15px;
}

@media screen and (min-width: 576px) {
	.largertypography .expand-lists .process-lists li .toggle-content {
		padding-top: 30px;
	}
}

.largertypography .expand-lists .process-lists li .toggle-content a,
.largertypography .expand-lists .process-lists li .toggle-content span {
	display: block;
	font-size: 18px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	margin-bottom: 5px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	color: #331272;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.largertypography .expand-lists .process-lists li .toggle-content a:hover {
	text-decoration: underline;
	color: #d72882;
}
.largertypography .expand-lists .process-lists li .toggle-content a:hover span {
	color: #d72882;
	text-decoration: underline;
}

.largertypography .expand-lists .process-lists li .toggle-content a span {
	text-decoration: underline;
	display: block;
	max-width: 100%;
	font-weight: bold;
	font-size: 18px;
}

.largertypography .expand-lists .process-lists li .toggle-content a span.badge {
	text-decoration: none;
	height: 24px;
	font-size: 14px;
	line-height: 24px;
	background: #eff0f0;
	border-radius: 30px;
	color: #111;
	padding: 0 10px;
	margin-top: 10px;
	margin-bottom: 10px;
}

.largertypography .expand-lists .process-lists li:last-child .toggle-content {
	border-bottom: 0;
	padding-bottom: 0;
}

@media screen and (min-width: 576px) {
	.largertypography .expand-lists .process-lists li:last-child {
		border-bottom: 2px solid #d8d8d8;
	}
	.largertypography .expand-lists .process-lists li:last-child .list-wrapper {
		border-bottom: 0;
		padding-bottom: 0;
	}
}

.largertypography .expand-lists .process-lists li:last-child:after {
	display: none;
}

@media screen and (min-width: 768px) {
	.largertypography .expand-lists .process-lists:not(.small) {
		margin-top: 10px;
	}
	.largertypography .expand-lists .process-lists:not(.small) li {
		padding-left: 80px;
	}
	.largertypography .expand-lists .process-lists:not(.small) li .count {
		width: 46px;
		height: 46px;
		line-height: 46px;
		font-size: 24px;
		-webkit-box-shadow: 0 0 0px 5px #fff;
		box-shadow: 0 0 0px 5px #fff;
	}
	.largertypography .expand-lists .process-lists:not(.small) li:after {
		left: 22px;
		top: 46px;
		height: calc(100% - 46px);
	}
	.largertypography .expand-lists .process-lists:not(.small) li .list-header h2 {
		font-size: 28px;
	}
	.largertypography .expand-lists .process-lists:not(.small) li .toggle-content a {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
	}

	.largertypography .expand-lists .process-lists:not(.small) li .toggle-content a span.badge {
		margin-left: 10px;
		margin-top: 0;
	}
}

@media print {
	.largertypography .expand-lists .trigger-process-list .show-mobile,
	.largertypography .expand-lists .trigger-process-list .btn {
		display: none;
	}
	.largertypography .expand-lists .process-lists li .list-header a {
		display: none;
	}
	.largertypography .expand-lists .process-lists li .toggle-content {
		display: block !important;
	}
	.largertypography .expand-lists.sidebar {
		display: none !important;
	}
}

/*  additional styles for multistep nav */

.largertypography .expand-lists .process-lists li.list-expanded a.showLink,
.largertypography .expand-lists .process-lists li a.hideLink {
	display: none;
}

.largertypography .expand-lists .process-lists li.list-expanded a.hideLink,
.largertypography .expand-lists .process-lists li a.showLink {
	display: inline;
}

.largertypography .expand-lists .trigger-process-list:not(.small) a:not(.active) {
	display: none;
}

.largertypography .expand-lists .process-lists li.list-expanded .toggle-content {
	display: block;
}

.largertypography .custom-list {
	position: relative;
	list-style: none;
	padding: 0;
	margin: 0;
	margin-bottom: 50px;
}

.largertypography .custom-list li {
	padding-left: 35px;
	position: relative;
	margin-bottom: 5px;
}

.largertypography .custom-list li p.no-link {
	text-decoration: none;
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #111111;
}

.largertypography .custom-list li:before {
	content: '';
	position: absolute;
	width: 16px;
	height: 2px;
	top: 12px;
	left: 0px;
	background: #d8d8d8;
}

.largertypography .custom-list li a {
	font-weight: bold;
	font-size: 18px;
	text-decoration: underline;
	color: #331272;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.largertypography .custom-list li a:hover {
	color: #d72882;
}

.nmc-register-responsive {
	position: relative;
	display: block;
	width: 100%;
	padding: 0;
	overflow: hidden;
}

.nmc-register-responsive::before {
	display: block;
	content: '';
}

.nmc-register-responsive .embed-responsive-item,
.nmc-register-responsive iframe,
.nmc-register-responsive embed,
.nmc-register-responsive object,
.nmc-register-responsive video {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border: 0;
	opacity: 0;
}

.nmc-register-responsive.video-embed {
	margin-bottom: 30px;
}

/*.largertypography .content-wrapper img {
  margin-bottom: 30px;
}*/

.nmc-register-responsive.video-embed img {
	position: relative;
	-webkit-transition: -webkit-transform 0.3s ease;
	transition: -webkit-transform 0.3s ease;
	-o-transition: transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
	width: 50px;
}

.largertypography .content-wrapper .nmc-register-responsive.video-embed img {
	margin-bottom: 0px;
}

@media screen and (min-width: 768px) {
	.nmc-register-responsive.video-embed img {
		width: 60px;
	}
}

@media screen and (min-width: 991px) {
	.nmc-register-responsive.video-embed img {
		width: 84px;
	}
}

@media screen and (min-width: 768px) {
	.nmc-register-responsive.video-embed {
		margin-bottom: 50px;
	}
	/*.largertypography .content-wrapper img {
      margin-bottom: 50px;
    }*/
	.nmc-register-responsive.video-embed:hover .overlay {
		opacity: 0;
	}
}

.nmc-register-responsive.video-embed .overlay {
	position: absolute;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	left: 0;
	top: 0;
	z-index: 3;
	-webkit-transition: opacity 0.3s ease;
	-o-transition: opacity 0.3s ease;
	transition: opacity 0.3s ease;
}

.nmc-register-responsive.video-embed .bg-image {
	text-align: center;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	z-index: 4;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	opacity: 1;
	visibility: inherit;
	background-size: cover !important;
}

.nmc-register-responsive.video-embed .bg-image.video-active {
	opacity: 0;
	visibility: hidden;
}

.nmc-register-responsive.video-embed .bg-image h3 {
	margin-top: 10px;
	font-size: 24px;
}

.nmc-register-responsive.video-embed .bg-image a {
	z-index: 4;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	color: #fff !important;
}

.nmc-register-responsive.video-embed .bg-image a:hover,
.nmc-register-responsive.video-embed .bg-image a:active,
.nmc-register-responsive.video-embed .bg-image a:focus {
	text-decoration: none;
	color: #fff !important;
}

@media screen and (min-width: 768px) {
	.nmc-register-responsive.video-embed .bg-image a:hover img,
	.nmc-register-responsive.video-embed .bg-image a:active img,
	.nmc-register-responsive.video-embed .bg-image a:focus img {
		-webkit-transform: scale(1.05);
		-ms-transform: scale(1.05);
		transform: scale(1.05);
	}
	.nmc-register-responsive.video-embed .bg-image a:hover span,
	.nmc-register-responsive.video-embed .bg-image a:active span {
		opacity: 0;
	}
}

.nmc-register-responsive.video-embed .bg-image span {
	font-size: 21px;
	font-weight: bold;
	-webkit-transition: opacity 0.3s ease;
	-o-transition: opacity 0.3s ease;
	transition: opacity 0.3s ease;
	margin-top: 10px;
	font-family: 'Gotham bold', Arial, sans-serif;
}

@media screen and (min-width: 768px) {
	.nmc-register-responsive.video-embed .bg-image span {
		font-size: 24px;
	}
}

.nmc-register-responsive.video-embed .video-holder {
	z-index: 2;
}

.embed-responsive-21by9::before {
	padding-top: 42.85714%;
}

.embed-responsive-16by9::before {
	padding-top: 56.25%;
}

.embed-responsive-4by3::before {
	padding-top: 75%;
}

.embed-responsive-1by1::before {
	padding-top: 100%;
}

.video-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	z-index: 1401;
	overflow: auto;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-overflow-scrolling: touch;
}

.video-modal .nmc-register-responsive iframe,
.video-modal .nmc-register-responsive .embed-responsive-item {
	opacity: 1;
}

.video-modal.overview-modal {
	background: rgba(255, 255, 255, 0.8);
	z-index: 8001;
}

.video-modal.overview-modal .video-modal-content {
	background: #fff;
	width: 100%;
	padding: 40px 0px;
	margin-top: 0;
	margin-bottom: 0 !important;
	-webkit-box-shadow: 0 0 41px 0 #bebebe;
	box-shadow: 0 0 41px 0 #bebebe;
	margin-bottom: 20px;
}

@media screen and (min-width: 576px) {
	.video-modal.overview-modal .video-modal-content {
		margin-bottom: 50px;
		margin-top: 80px;
		padding: 40px 40px;
		margin-bottom: 40px;
	}
}

.video-modal.overview-modal .video-modal-content .close {
	right: 20px;
	top: 20px;
}

.video-modal.overview-modal .video-modal-content .modal-body {
	position: relative;
	padding-top: 20px;
}

.video-modal.overview-modal .video-modal-content .modal-body h1 {
	margin-bottom: 40px;
}

.video-modal.overview-modal .video-modal-content .modal-body .text-block h4 {
	font-size: 21px;
	padding-bottom: 10px;
	margin-top: 0;
}

@media screen and (min-width: 991px) {
	.video-modal.overview-modal .video-modal-content {
		max-width: 794px;
		padding: 40px 60px;
	}
}

.video-modal.modal-open {
	opacity: 1;
	visibility: visible;
}

.video-modal .video-modal-content {
	position: relative;
	max-width: 1020px;
	padding: 0 60px;
	margin-top: 80px;
	margin-left: auto;
	margin-right: auto;
}

.video-modal .video-modal-content .close {
	position: absolute;
	right: 10px;
	top: 0;
	opacity: 1;
}

.video-modal .video-modal-content .close img {
	width: 25px;
}

@media screen and (min-width: 1600px) {
	.video-modal .video-modal-content {
		max-width: 1280px;
	}
	.video-modal .video-modal-content .close img {
		width: 25px;
	}
}

.video-modal .video-modal-content .nmc-register-responsive {
	width: 100% !important;
}

@media print {
	.nmc-register-responsive {
		display: none !important;
	}
	.video-meta {
		margin-bottom: 20px;
	}
	.video-meta img {
		max-width: 250px !important;
	}
}

.largertypography .siderbar-header .text-underline.dark-color {
	line-height: 1;
	margin-bottom: 0px;
}

.largertypography .siderbar-header a {
	display: block;
	font-size: 18px;
	text-decoration: underline;
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #331272;
}

.largertypography .siderbar-header a:hover {
	text-decoration: underline;
	color: #d72882;
}

.largertypography .text-block,
.largertypography .content-wrapper {
	position: relative;
	margin-bottom: 0;
	padding-bottom: 0;
}

.largertypography .text-block.has-primary-link,
.largertypography .content-wrapper.has-primary-link {
	margin-bottom: 40px;
}

.largertypography .text-block.sidebar,
.largertypography .content-wrapper.sidebar {
	margin-top: 50px;
}

@media screen and (min-width: 991px) {
	.largertypography .text-block.sidebar,
	.largertypography .content-wrapper.sidebar {
		margin-top: 0;
	}
}

.largertypography .text-block .with-icon,
.largertypography .content-wrapper .with-icon {
	margin-bottom: 30px;

	font-weight: normal;
}

.largertypography .text-block p:last-child,
.largertypography .content-wrapper p:last-child {
	margin-bottom: 15px;
}

@media screen and (min-width: 576px) {
	.largertypography .text-block h2,
	.largertypography .content-wrapper h2,
	.largertypography .text-block h3,
	.largertypography .content-wrapper h3 {
		margin-bottom: 20px;
		margin-top: 10px;
	}
}

.largertypography .text-block ul,
.largertypography .content-wrapper ul {
	margin-bottom: 30px;
}

.largertypography .text-block p:not(.text-underline),
.largertypography .content-wrapper p:not(.text-underline) {
	padding-bottom: 5px;
	display: inline-block;
	font-size: 18px;
	line-break: 27px;
}

.largertypography .text-block ul:not(.process-lists):not(.check),
.largertypography .content-wrapper ul:not(.process-lists):not(.check) {
	list-style: none;
	padding-left: 0;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page {
	padding-top: 5px;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page.structure-list li:last-child,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page.structure-list li:last-child {
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li {
	margin: 0;
	border-top: 1px solid #d8d8d8;
	-webkit-transition: background 0.3s ease;
	-o-transition: background 0.3s ease;
	transition: background 0.3s ease;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li a,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li a {
	text-decoration: none;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li a img,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li a img {
	display: inline-block;
	margin-left: 6px;
	position: relative;
	top: -1px;
	width: 16px;
	margin-bottom: 0px;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:last-child,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:last-child {
	border-bottom: 1px solid #d8d8d8;
}

@media screen and (min-width: 991px) {
	.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:last-child,
	.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:last-child {
		border-bottom: 0;
	}
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:hover,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:hover {
	background: #eff0f0;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:before,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:before {
	display: none;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:after,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:after {
	content: '';
	position: absolute;
	top: 21px;
	left: 5px;
	background: url('/static/img/chevron-left.svg') center center no-repeat;
	background-size: 8px 13px;
	width: 8px;
	height: 13px;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li a,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li a {
	color: #00334c;
	font-weight: bold;
	padding-top: 15px;
	padding-bottom: 15px;
	display: block;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li.file-type,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li.file-type {
	padding-left: 45px;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li.file-type:after,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li.file-type:after {
	/*background: url("/static/img/file-pdf.svg"); */
	background-size: 25px 26px;
	width: 25px;
	height: 26px;
	top: 14px;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li.file-type a,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li.file-type a {
	text-decoration: none;

	font-weight: normal;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li.file-type a span:not(.file-name),
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li.file-type a span:not(.file-name) {
	text-decoration: underline;

	font-weight: normal;
}

.largertypography .text-block ul:not(.process-lists):not(.check).related-page li.file-type a .file-name,
.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li.file-type a .file-name {
	color: #666d70;
	padding-left: 10px;
	font-family: 'Foundry Monoline W01 Md', arial, sans-serif;
	font-weight: normal;
}

.largertypography .text-block ul:not(.process-lists):not(.check) li,
.largertypography .content-wrapper ul:not(.process-lists):not(.check) li {
	position: relative;
	padding-left: 20px;
	font-size: 18px;
	margin-bottom: 15px;
}

.largertypography .text-block ul:not(.process-lists):not(.check) li a,
.largertypography .content-wrapper ul:not(.process-lists):not(.check) li a {
	text-decoration: underline;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.largertypography .text-block ul:not(.process-lists):not(.check) li a:hover,
.largertypography .content-wrapper ul:not(.process-lists):not(.check) li a:hover {
	text-decoration: underline;
	color: #331272;
}

.largertypography .text-block ul:not(.process-lists):not(.check) li a:visited,
.largertypography .content-wrapper ul:not(.process-lists):not(.check) li a:visited {
	color: #331272;
}

.largertypography .text-block ul:not(.process-lists):not(.check) li:before,
.largertypography .content-wrapper ul:not(.process-lists):not(.check) li:before {
	content: '';
	position: absolute;
	left: 0;
	top: 10px;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #111111;
}

ul.largertypography:not(.process-lists):not(.check) {
	list-style: none;
	padding-left: 0;
}

ul.largertypography:not(.process-lists):not(.check).related-page {
	padding-top: 5px;
	margin-bottom: 30px;
}

ul.largertypography:not(.process-lists):not(.check).related-page.structure-list li a {
	text-decoration: underline;
}

ul.largertypography:not(.process-lists):not(.check).related-page.structure-list li:last-child {
	border-bottom: 1px solid #d8d8d8;
}

ul.largertypography:not(.process-lists):not(.check).related-page li {
	margin: 0;
	border-top: 1px solid #d8d8d8;
	-webkit-transition: background 0.3s ease;
	-o-transition: background 0.3s ease;
	transition: background 0.3s ease;
}

ul.largertypography:not(.process-lists):not(.check).related-page li a {
	text-decoration: none;
}

ul.largertypography:not(.process-lists):not(.check).related-page li a img {
	display: inline-block;
	margin-left: 6px;
	position: relative;
	top: -1px;
	width: 16px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li:last-child {
	border-bottom: 1px solid #d8d8d8;
}

@media screen and (min-width: 991px) {
	ul.largertypography:not(.process-lists):not(.check).related-page li:last-child {
		border-bottom: 0;
	}
}

ul.largertypography:not(.process-lists):not(.check).related-page li:before {
	display: none;
}

ul.largertypography:not(.process-lists):not(.check).related-page li:after {
	content: '';
	position: absolute;
	top: 21px;
	left: 5px;
	background: url('/static/img/refresh/chevron.svg') center center no-repeat;
	background-size: 8px 15px;
	width: 8px;
	height: 13px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li a {
	padding-top: 15px;
	padding-bottom: 15px;
	display: block;
	color: #d72882;
	font-family: 'Gotham bold', Arial, sans-serif;
}
ul.largertypography:not(.process-lists):not(.check).related-page li a:hover {
	color: #31006f;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type {
	padding-left: 45px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type:after {
	/* background: url("/static/img/file-pdf.svg");  StructuredListBlock Different Icons NES */
	background-size: 25px 26px;
	width: 25px;
	height: 26px;
	top: 14px;
}

/* NES StructuredListBlock Different Icons START */

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-pdf:after {
	background: url(/static/img/refresh/pdf-icon-pink.svg) no-repeat;
	background-size: 22px 24px;
	width: 22px;
	height: 24px;
	top: 18px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-ppt:after {
	background: url('/static/img/files/ppt.png');
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	top: 18px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-word:after {
	background: url('/static/img/files/w.png');
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	top: 18px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-xl:after {
	background: url('/static/img/files/xl.png');
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	top: 18px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-csv:after {
	background: url('/static/img/files/csv.png');
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	top: 18px;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type-unknown:after {
	background: url('/static/img/files/html.png');
	background-size: 20px 20px;
	width: 20px;
	height: 20px;
	top: 18px;
	left: 7px;
}

/* NES StructuredListBlock Different Icons END */

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type a {
	text-decoration: none;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type a span:not(.file-name) {
	text-decoration: underline;
}

ul.largertypography:not(.process-lists):not(.check).related-page li.file-type a .file-name {
	font-weight: normal;
	color: #666d70;
	padding-left: 10px;
	font-family: 'Gotham book', Arial, sans-serif;
}

ul.largertypography:not(.process-lists):not(.check) li {
	position: relative;
	padding-left: 20px;
	font-size: 18px;
	margin-bottom: 10px;
}

ul.largertypography:not(.process-lists):not(.check) li a {
	color: #d10074;
	text-decoration: underline;
	font-weight: bold;
}

ul.largertypography:not(.process-lists):not(.check) li:before {
	content: '';
	position: absolute;
	left: 0;
	top: 10px;
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background: #111111;
}

.text-block-group {
	position: relative;
}

.text-block-group p {
	padding-bottom: 0 !important;
	margin-bottom: 20px;
}

@media print {
	.largertypography .text-block ul:not(.process-lists):not(.check) li:before,
	.largertypography .content-wrapper ul:not(.process-lists):not(.check) li:before {
		content: '●';
		background: none !important;
		top: 0;
	}
	.largertypography .text-block .with-icon,
	.largertypography .content-wrapper .with-icon {
		color: black !important;
		background: none;
		padding-left: 0;
		text-align: left;
	}
	.largertypography .text-block ul:not(.process-lists):not(.check).related-page li,
	.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li {
		padding-left: 0 !important;
	}
	.largertypography .text-block ul:not(.process-lists):not(.check).related-page li:after,
	.largertypography .content-wrapper ul:not(.process-lists):not(.check).related-page li:after {
		display: none !important;
	}
}

.largertypography .cta-group {
	margin-bottom: 40px;
}

.largertypography .cta-group.tabular-list {
	position: relative;
}

.largertypography .cta-group.tabular-list .cta-bar {
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	padding: 20px 0;
}

.largertypography .cta-group.tabular-list .cta-bar.tabular-header {
	display: none;
}

@media screen and (min-width: 768px) {
	.largertypography .cta-group.tabular-list .cta-bar.tabular-header {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
	}
}

.largertypography .cta-group.tabular-list .cta-bar.tabular-header h6 {
	margin: 0;
}

@media screen and (min-width: 768px) {
	.largertypography .cta-group.tabular-list .cta-bar p {
		padding-right: 80px;
	}
}

.largertypography .cta-group.tabular-list .cta-bar p a {
	color: #d10074;
	text-decoration: underline;
	font-weight: bold;
}

.largertypography .cta-group.tabular-list .cta-bar .right {
	padding-top: 20px;
}

@media screen and (min-width: 768px) {
	.largertypography .cta-group.tabular-list .cta-bar .right {
		padding-top: 0;
	}
	.largertypography .cta-group.tabular-list .cta-bar .right h6 {
		text-align: right;
	}
}

.largertypography .cta-group.tabular-list .cta-bar .right p {
	font-size: 14px;
	padding-right: 0;
}

@media screen and (min-width: 768px) {
	.largertypography .cta-group {
		margin-bottom: 0;
	}
}

.largertypography .cta-group .cta-bar {
	border-bottom: 0;
}

.largertypography .cta-group .cta-bar:last-child {
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .cta-group .cta-bar h6 {
	margin-bottom: 5px;
	font-weight: bold;
}

.largertypography .cta-group .cta-bar p {
	font-size: 16px;
	padding-bottom: 0;
	margin-bottom: 0 !important;
}

.largertypography .btn-ghost {
	border: 1px solid #d8d8d8;
	font-size: 14px !important;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	min-width: 155px;
	margin-top: 20px;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

@media screen and (min-width: 580px) {
	.largertypography .btn-ghost {
		margin-top: 0;
	}
	.largertypography .btn-ghost:hover {
		color: #d10074 !important;
		text-decoration: underline !important;
	}
}

.largertypography .btn-ghost.right-icon img {
	margin-left: 10px;
	margin-right: 0;
	top: -3px;
	width: 18px;
}

.largertypography .btn-ghost img {
	margin-right: 10px;
	position: relative;
	top: -1px;
}

.largertypography .cta {
	position: relative;
	margin: 0 0 40px;
}

.largertypography .cta a {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 3px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.largertypography .cta a span {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.largertypography .cta a .next {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #111111;
	/*WG*/
}

.largertypography .cta.next-page a .small {
	text-align: right !important;
}

.largertypography .cta a .prev {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-family: 'Gotham bold', Arial, sans-serif;
	color: #111111;
	/*WG*/
}

.largertypography .cta a img {
	margin-right: 10px;
	margin-left: 0;
	margin-bottom: 0px;
	width: 15px;
	height: 12px;
	/*WG*/
}

.largertypography .cta a .small {
	font-size: 16px;
	text-decoration: underline;
	margin-bottom: 0;
	padding-left: 25px;
	color: #331272;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.largertypography .cta a:hover {
	text-decoration: none;
}
.largertypography .cta a:hover .small {
	text-decoration: none;
}

@media screen and (min-width: 768px) {
	.largertypography .cta a {
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end;
	}
	.largertypography .cta a .next {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
		color: #111111;
	}
	.largertypography .cta a img {
		margin-left: 10px;
		margin-right: 0;
		margin-bottom: 0px;
	}
	.largertypography .cta a .small {
		padding-left: 0;
	}
}

.largertypography .cta-bar {
	position: relative;
	padding: 25px 0;
	border-top: 1px solid #d8d8d8;
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .cta-bar .btn {
	width: 100%;
}

.largertypography .cta-bar p {
	margin-bottom: 0;
	padding-bottom: 15px;
}

@media screen and (min-width: 580px) {
	.largertypography .cta-bar {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
	.largertypography .cta-bar p {
		margin: 0;
		padding-bottom: 0 !important;
	}
	.largertypography .cta-bar .btn {
		width: auto;
	}
}

.largertypography .pagination {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
}

.largertypography .pagination .cta {
	margin-bottom: 20px;
}

.largertypography .pagination .cta a {
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.largertypography .pagination .cta a .prev img {
	margin-right: 10px;
	margin-left: 0;
	margin-bottom: 0px;
}

.largertypography .pagination .cta a .prev img.rotate-180 {
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
}

@media screen and (min-width: 768px) {
	.largertypography .pagination {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
	.largertypography .pagination.no-prev {
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end;
	}
	.largertypography .pagination .cta {
		margin-bottom: 0;
	}
}

.cta-group.largertypography {
	margin-bottom: 40px;
}

.cta-group.largertypography.tabular-list {
	position: relative;
	margin-bottom: 50px;
}

.cta-group.largertypography.tabular-list .cta-bar {
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	padding: 20px 0;
}

.cta-group.largertypography.tabular-list .cta-bar.tabular-header {
	display: none;
}

@media screen and (min-width: 768px) {
	.cta-group.largertypography.tabular-list .cta-bar.tabular-header {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
	}
}

.cta-group.largertypography.tabular-list .cta-bar.tabular-header h6 {
	margin: 0;
}

@media screen and (min-width: 768px) {
	.cta-group.largertypography.tabular-list .cta-bar p {
		padding-right: 80px;
	}
}

.cta-group.largertypography.tabular-list .cta-bar p a {
	color: #d10074;
	text-decoration: underline;
	font-weight: bold;
}

.cta-group.largertypography.tabular-list .cta-bar .right {
	padding-top: 20px;
}

@media screen and (min-width: 768px) {
	.cta-group.largertypography.tabular-list .cta-bar .right {
		padding-top: 0;
	}
	.cta-group.largertypography.tabular-list .cta-bar .right h6 {
		text-align: right;
	}
}

.cta-group.largertypography.tabular-list .cta-bar .right p {
	font-size: 14px;
	padding-right: 0;
}

@media screen and (min-width: 768px) {
	.cta-group.largertypography {
		margin-bottom: 0;
	}
}

.cta-group.largertypography .cta-bar {
	border-bottom: 0;
}

.cta-group.largertypography .cta-bar:last-child {
	border-bottom: 1px solid #d8d8d8;
}

.cta-group.largertypography .cta-bar h6 {
	margin-bottom: 5px;
	font-weight: normal;
}

.cta-group.largertypography .cta-bar p {
	font-size: 16px;
	padding-bottom: 0;
	margin-bottom: 0 !important;
}

.btn-ghost {
	border: 1px solid #d8d8d8;
	font-size: 14px !important;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	min-width: 155px;
	margin-top: 20px;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

@media screen and (min-width: 580px) {
	.btn-ghost {
		margin-top: 0;
	}
	.btn-ghost:hover {
		color: #d10074 !important;
		text-decoration: underline !important;
	}
}

.btn-ghost.right-icon img {
	margin-left: 10px;
	margin-right: 0;
	top: -3px;
	width: 18px;
}

.btn-ghost img {
	margin-right: 10px;
	position: relative;
	top: -1px;
}

.cta {
	position: relative;
	margin: 0 0 40px;
}

.cta a {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 3px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.cta a span {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.cta a .next {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: reverse;
	-ms-flex-direction: row-reverse;
	flex-direction: row-reverse;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}

.cta a .prev {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.cta a img {
	margin-right: 10px;
	margin-left: 0;
}

.cta a .small {
	font-size: 16px;
	text-decoration: underline;
	margin-bottom: 0;
	padding-left: 25px;
}

@media screen and (min-width: 768px) {
	.cta a {
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end;
	}
	.cta a .next {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
	}
	.cta a img {
		margin-left: 10px;
		margin-right: 0;
	}
	.cta a .small {
		padding-left: 0;
	}
}

.largertypography.cta-bar {
	position: relative;
	padding: 25px 0;
	border-top: 1px solid #d8d8d8;
	border-bottom: 1px solid #d8d8d8;
}

.largertypography.cta-bar .btn {
	width: 100%;
}

.largertypography.cta-bar p {
	margin-bottom: 0;
	padding-bottom: 15px;
}

@media screen and (min-width: 580px) {
	.largertypography.cta-bar {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
	.largertypography.cta-bar p {
		margin: 0;
		padding-bottom: 0 !important;
	}
	.largertypography.cta-bar .btn {
		width: auto;
	}
}

.largertypography.pagination {
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
}

.largertypography.pagination .cta {
	margin-bottom: 20px;
}

.largertypography.pagination .cta a {
	-webkit-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
}

.largertypography.pagination .cta a .prev img {
	margin-right: 10px;
	margin-left: 0;
}

.largertypography.pagination .cta a .prev img.rotate-180 {
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
}

@media screen and (min-width: 768px) {
	.largertypography.pagination {
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-ms-flex-direction: row;
		flex-direction: row;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
	.largertypography.pagination.no-prev {
		-webkit-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end;
	}
	.largertypography.pagination .cta {
		margin-bottom: 0;
	}
}

@media print {
	.largertypography .cta-bar .btn {
		width: auto;
		color: black !important;
		padding: 0 !important;
		border: 0;
		padding-left: 0 !important;
		text-align: left;
		display: inline-block;
	}
	.largertypography .cta-bar .btn img {
		display: none;
	}
	.largertypography .pagination .cta a {
		display: block;
	}
	.largertypography .pagination .cta .next img,
	.largertypography .pagination .cta .prev img {
		display: none;
	}
	.largertypography .pagination .cta .next:before {
		content: '→';
	}
	.largertypography .pagination .cta .prev:before {
		content: '←';
	}
	.largertypography .pagination .cta .small {
		padding: 0 !important;
	}
}

.largertypography .highlight {
	position: relative;
	padding: 35px 30px;
	margin-bottom: 30px;
}

@media screen and (min-width: 576px) {
	.largertypography .highlight {
		margin-bottom: 50px;
	}
}

.largertypography .highlight p {
	margin-bottom: 0 !important;
	padding-bottom: 0 !important;
}

.largertypography .highlight h1 {
	margin-top: 0;
	padding-top: 5px;
	margin-bottom: 0;
}

.largertypography .highlight.small-gap h3 {
	padding-bottom: 0;
	margin-bottom: 0;
}

.largertypography .highlight.small-gap p {
	margin: 0;
}

.largertypography .highlight.small-gap a {
	text-decoration: underline;
	font-size: 18px;
}

.largertypography .highlight h3 {
	font-size: 24px;
	padding-bottom: 10px;
	line-height: 29px;
}

.largertypography .highlight p {
	font-size: 18px;
}

.largertypography .highlight a {
	color: #d10074;
	font-weight: bold;
}

.largertypography .highlight.warning {
	border-left: 16px solid #feea00;
	background: #fffbcc;
}

.largertypography .highlight.info {
	border-left: 16px solid #00838f;
	background: #e5f8fa;
}

.largertypography.highlight {
	position: relative;
	padding: 35px 30px;
	margin-bottom: 30px;
}

@media screen and (min-width: 576px) {
	.largertypography.highlight {
		margin-bottom: 50px;
	}
}

.largertypography.highlight p {
	margin-bottom: 0 !important;
	padding-bottom: 0 !important;
}

.largertypography.highlight h1 {
	margin-top: 0;
	padding-top: 5px;
	margin-bottom: 0;
}

.largertypography.highlight.small-gap h3 {
	padding-bottom: 0;
	margin-bottom: 0;
}

.largertypography.highlight.small-gap p {
	margin: 0;
}

.largertypography.highlight.small-gap a {
	text-decoration: underline;
	font-size: 18px;
}

.largertypography.highlight h3 {
	font-size: 24px;
	padding-bottom: 10px;
	line-height: 29px;
}

.largertypography.highlight p {
	font-size: 18px;
}

.largertypography.highlight a {
	color: #d10074;
	font-weight: bold;
}

.largertypography.highlight.warning {
	border-left: 16px solid #feea00;
	background: #fffbcc;
}

.largertypography .accordion {
	position: relative;
	margin-bottom: 20px;
}

@media screen and (min-width: 576px) {
	.largertypography .accordion {
		margin-bottom: 50px;
	}
}

.largertypography .accordion.result-page {
	margin-bottom: 20px;
}

.largertypography .accordion.result-page .single {
	background: #ecf2e7;
}

.largertypography .accordion.result-page .single.fail {
	background: #faebe5;
}

.largertypography .accordion.result-page .single.fail:hover,
.largertypography .accordion.result-page .single.fail.active {
	background: #faebe5;
}

.largertypography .accordion.result-page .single.fail .ac-toggle:before {
	content: '';
	width: 25px;
	height: 25px;
	top: 16px;
	color: #b44113;
	background: url('/static/img/info-red.svg') center center no-repeat;
	background-size: 25px 25px;
}

@media (max-width: 768px) {
	.largertypography .accordion.result-page .single.fail .ac-toggle:before {
		top: 15px;
	}
}

.largertypography .accordion.result-page .single .inner {
	background: transparent;
	padding-left: 60px;
	padding-top: 0px;
}

.largertypography .accordion.result-page .single .inner p {
	padding-bottom: 0;
}

@media screen and (min-width: 991px) {
	.largertypography .accordion.result-page .single .inner {
		padding-right: 100px;
	}
}

@media (max-width: 768px) {
	.largertypography .accordion.result-page .single .inner {
		padding-left: 40px;
		padding-bottom: 0;
	}
}

.largertypography .accordion.result-page .single:before {
	display: none;
}

.largertypography .accordion.result-page .single:hover,
.largertypography .accordion.result-page .single.active {
	background: #ecf2e7;
}

.largertypography .accordion.result-page .single .ac-toggle {
	padding-left: 60px;
	font-weight: bold;
}

.largertypography .accordion.result-page .single .ac-toggle.active {
	border-color: transparent;
}

.largertypography .accordion.result-page .single .ac-toggle.active:after {
	background: url('/static/img/result-arrow-active.png') center center no-repeat;
	background-size: 12px 7px;
}

.largertypography .accordion.result-page .single .ac-toggle:before {
	content: '';
	position: absolute;
	left: 20px;
	top: 18px;
	background: url('/static/img/check.svg') center center no-repeat;
	background-size: contain;
	width: 25px;
	height: 18px;
}

.largertypography .accordion.result-page .single .ac-toggle:after {
	content: '';
	height: 7px;
	width: 12px;
	background: url('/static/img/result-arrow.png') center center no-repeat;
	background-size: 12px 7px;
	right: 16px;
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);
	top: 26px;
}

@media (max-width: 768px) {
	.largertypography .accordion.result-page .single .ac-toggle {
		padding-left: 40px;
	}
	.largertypography .accordion.result-page .single .ac-toggle:before {
		left: 13px;
		width: 20px;
		height: 13px;
		top: 21px;
	}
}

.largertypography .accordion.alter {
	margin-bottom: 30px;
}

@media screen and (min-width: 991px) {
	.largertypography .accordion.alter {
		margin-bottom: 50px;
	}
}

.largertypography .accordion.alter .single .ac-toggle {
	padding-left: 0;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.largertypography .accordion.alter .single:hover .ac-toggle,
.largertypography .accordion.alter .single.active .ac-toggle {
	padding-left: 25px;
}

.largertypography .accordion.blueprint {
	margin-bottom: 0;
}

.largertypography .accordion.blueprint .top {
	padding-top: 20px;
	padding-bottom: 20px;
}

.largertypography .accordion.blueprint .single {
	background: #eff0f0;
}

.largertypography .accordion.blueprint .single .dropdown p:before {
	content: '';
	position: relative;
	width: 12px;
	height: 7px;
	background: url('/static/img/chevron-down.svg') center center no-repeat;
	background-size: cover;
	margin-right: 10px;
}

.largertypography .accordion.blueprint .single:before {
	display: none;
}

.largertypography .accordion.blueprint .single tbody tr:last-child {
	border-bottom: 0;
}

.largertypography .accordion.blueprint .single .ac-toggle .icon-holder {
	position: absolute;
	right: 0;
	top: 0;
	width: 60px;
	height: calc(100% + 1px);
	background: #d10074;
	z-index: 0;
}

.largertypography .accordion.blueprint .single .ac-toggle:before,
.largertypography .accordion.blueprint .single .ac-toggle:after {
	background: #fff;
	z-index: 1;
	right: 24px;
}

@media (max-width: 991px) {
	.largertypography .accordion.blueprint .single .ac-toggle {
		font-size: 0;
		padding-left: 15px;
	}
	.largertypography .accordion.blueprint .single .ac-toggle span {
		font-size: 21px;
	}
}

.largertypography .accordion.blueprint .single .inner {
	padding: 0;
}

.largertypography .accordion.blueprint .single .inner > .td-content {
	display: none;
}

.largertypography .accordion.blueprint .single .inner .table-responsive {
	margin: 0;
}

.largertypography .accordion.blueprint .single .inner .dropdown {
	position: relative;
}

.largertypography .accordion.blueprint .single .inner .dropdown p {
	padding: 15px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	font-size: 16px;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin: 0;
}

.largertypography .accordion.blueprint .single .inner .dropdown p i {
	color: #d10074;
	margin-right: 5px;
}

.largertypography .accordion .single {
	width: 100%;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .accordion .single.active {
	background: #eff0f0;
}

.largertypography .accordion .single.active:before {
	opacity: 1;
}

.largertypography .accordion .single:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 6px;
	height: 100%;
	background: #8917a6;
	opacity: 0;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	z-index: 2;
}

.largertypography .accordion .single:hover {
	background: #eff0f0;
}

.largertypography .accordion .single:hover:before {
	opacity: 1;
}

.largertypography .accordion .single:first-child {
	border-top: 1px solid #d8d8d8;
}

.largertypography .accordion .single .inner {
	/*display: none; NES*/
	background: #fff;
	padding: 25px;
	padding-bottom: 10px;
}

.largertypography .accordion .single .inner h1,
.largertypography .accordion .single .inner h2,
.largertypography .accordion .single .inner h3,
.largertypography .accordion .single .inner h4,
.largertypography .accordion .single .inner h5,
.largertypography .accordion .single .inner h6 {
	margin-top: 0;
}

.largertypography .accordion .single .inner p {
	font-weight: 500;
	font-size: 18px;
}

.largertypography .accordion .single .inner a:not(.btn) {
	text-decoration: underline;
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.largertypography .accordion .single .inner a:not(.btn):hover {
	text-decoration: none;
}

.largertypography .accordion .single .ac-toggle {
	position: relative;
	display: block;
	padding: 16px 0;
	padding-right: 40px;
	font-size: 18px;
	color: #111111;
	padding-left: 25px;
	padding-right: 50px;
	z-index: 1;
	border-bottom: 1px solid transparent;
	font-family: 'Foundry Monoline W01 Md', arial, sans-serif;
	font-weight: normal;
}

.largertypography .accordion .single .ac-toggle:focus {
	text-decoration: none;
}

.largertypography .accordion .single .ac-toggle span {
	font-weight: normal;
	padding-right: 5px;
}

.largertypography .accordion .single .ac-toggle:hover {
	text-decoration: none;
}

.largertypography .accordion .single .ac-toggle.active {
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .accordion .single .ac-toggle.active:after {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);
}

.largertypography .accordion .single .ac-toggle:before {
	content: '';
	position: absolute;
	right: 20px;
	top: 50%;
	width: 12px;
	height: 2px;
	background: #8917a6;
}

.largertypography .accordion .single .ac-toggle:after {
	content: '';
	position: absolute;
	right: 20px;
	top: 50%;
	width: 12px;
	height: 2px;
	background: #8917a6;
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	transform: rotate(90deg);
	-webkit-transition: -webkit-transform 0.3s ease;
	transition: -webkit-transform 0.3s ease;
	-o-transition: transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}

.largertypography.accordion {
	position: relative;
	margin-bottom: 20px;
}

.largertypography .how-to-register .primary-link,
.largertypography .how-to-register .primary-link:visited {
	margin-left: 20px;
	color: #31006f;
	font-family: 'Gotham bold', Arial, sans-serif;
}

.how-to-register.hub .media {
	border: 1px solid #d1d3d4;
}

@media screen and (min-width: 576px) {
	.largertypography.accordion {
		margin-bottom: 50px;
	}
}

.largertypography.accordion.result-page {
	margin-bottom: 20px;
}

.largertypography.accordion.result-page .single {
	background: #ecf2e7;
}

.largertypography.accordion.result-page .single.fail {
	background: #faebe5;
}

.largertypography.accordion.result-page .single.fail:hover,
.largertypography.accordion.result-page .single.fail.active {
	background: #faebe5;
}

.largertypography.accordion.result-page .single.fail .ac-toggle:before {
	background: url('/static/img/info-red.svg') center center no-repeat;
	background-size: 25px 25px;
}

@media (max-width: 768px) {
	.largertypography.accordion.result-page .single.fail .ac-toggle:before {
		top: 15px;
	}
}

.largertypography.accordion.result-page .single .inner {
	background: transparent;
	padding-left: 60px;
	padding-top: 0px;
}

.largertypography.accordion.result-page .single .inner p {
	padding-bottom: 0;
}

@media screen and (min-width: 991px) {
	.largertypography.accordion.result-page .single .inner {
		padding-right: 100px;
	}
}

@media (max-width: 768px) {
	.largertypography.accordion.result-page .single .inner {
		padding-left: 40px;
		padding-bottom: 0;
	}
}

.largertypography.accordion.result-page .single:before {
	display: none;
}

.largertypography.accordion.result-page .single:hover,
.largertypography.accordion.result-page .single.active {
	background: #ecf2e7;
}

.largertypography.accordion.result-page .single .ac-toggle {
	padding-left: 60px;
	font-weight: bold;
}

.largertypography.accordion.result-page .single .ac-toggle.active {
	border-color: transparent;
}

.largertypography.accordion.result-page .single .ac-toggle.active:after {
	background: url('/static/img/result-arrow-active.png') center center no-repeat;
	background-size: 12px 7px;
}

.largertypography.accordion.result-page .single .ac-toggle:before {
	content: '';
	position: absolute;
	left: 20px;
	top: 18px;
	background: url('/static/img/check.svg') center center no-repeat;
	background-size: contain;
	width: 25px;
	height: 18px;
}

.largertypography.accordion.result-page .single .ac-toggle:after {
	content: '';
	height: 7px;
	width: 12px;
	background: url('/static/img/result-arrow.png') center center no-repeat;
	background-size: 12px 7px;
	right: 16px;
	top: 26px;
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);
}

@media (max-width: 768px) {
	.largertypography.accordion.result-page .single .ac-toggle {
		padding-left: 40px;
	}
	.largertypography.accordion.result-page .single .ac-toggle:before {
		left: 13px;
		width: 20px;
		height: 13px;
		top: 21px;
	}
}

.largertypography.accordion.alter {
	margin-bottom: 30px;
}

@media screen and (min-width: 991px) {
	.largertypography.accordion.alter {
		margin-bottom: 50px;
	}
}

.largertypography.accordion.alter .single .ac-toggle {
	padding-left: 0;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
}

.largertypography.accordion.alter .single:hover .ac-toggle,
.largertypography.accordion.alter .single.active .ac-toggle {
	padding-left: 25px;
}

.largertypography.accordion.blueprint {
	margin-bottom: 0;
}

.largertypography.accordion.blueprint .top {
	padding-top: 20px;
	padding-bottom: 20px;
}

.largertypography.accordion.blueprint .single {
	background: #eff0f0;
}

.largertypography.accordion.blueprint .single .dropdown p:before {
	content: '';
	position: relative;
	width: 12px;
	height: 7px;
	background: url('/static/img/chevron-down.svg') center center no-repeat;
	background-size: cover;
	margin-right: 10px;
}

.largertypography.accordion.blueprint .single:before {
	display: none;
}

.largertypography.accordion.blueprint .single tbody tr:last-child {
	border-bottom: 0;
}

.largertypography.accordion.blueprint .single .ac-toggle .icon-holder {
	position: absolute;
	right: 0;
	top: 0;
	width: 60px;
	height: calc(100% + 1px);
	background: #d10074;
	z-index: 0;
}

.largertypography.accordion.blueprint .single .ac-toggle:before,
.largertypography.accordion.blueprint .single .ac-toggle:after {
	background: #fff;
	z-index: 1;
	right: 24px;
}

@media (max-width: 991px) {
	.largertypography.accordion.blueprint .single .ac-toggle {
		font-size: 0;
		padding-left: 15px;
	}
	.largertypography.accordion.blueprint .single .ac-toggle span {
		font-size: 21px;
	}
}

.largertypography.accordion.blueprint .single .inner {
	padding: 0;
}

.largertypography.accordion.blueprint .single .inner > .td-content {
	display: none;
}

.largertypography.accordion.blueprint .single .inner .table-responsive {
	margin: 0;
}

.largertypography.accordion.blueprint .single .inner .dropdown {
	position: relative;
}

.largertypography.accordion.blueprint .single .inner .dropdown p {
	padding: 15px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	font-size: 16px;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin: 0;
}

.largertypography.accordion.blueprint .single .inner .dropdown p i {
	color: #d10074;
	margin-right: 5px;
}

.largertypography.accordion .single {
	width: 100%;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	border-bottom: 1px solid #d8d8d8;
}

.largertypography.accordion .single.active {
	background: #eff0f0;
}

.largertypography.accordion .single.active:before {
	opacity: 1;
}

.largertypography.accordion .single:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 6px;
	height: 100%;
	background: #d10074;
	opacity: 0;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	z-index: 2;
}

.largertypography.accordion .single:hover {
	background: #eff0f0;
}

.largertypography.accordion .single:hover:before {
	opacity: 1;
}

.largertypography.accordion .single:first-child {
	border-top: 1px solid #d8d8d8;
}

.largertypography.accordion .single .inner {
	/*display: none; NES*/
	background: #fff;
	padding: 25px;
	padding-bottom: 10px;
}

.largertypography.accordion .single .inner h1,
.largertypography.accordion .single .inner h2,
.largertypography.accordion .single .inner h3,
.largertypography.accordion .single .inner h4,
.largertypography.accordion .single .inner h5,
.largertypography.accordion .single .inner h6 {
	margin-top: 0;
}

.largertypography.accordion .single .inner p {
	/*font-weight: 500; NES*/
	font-weight: normal;
	font-size: 18px;
}

.largertypography.accordion .single .inner a:not(.btn) {
	text-decoration: underline;
	color: #d10074;
	/*font-weight: bold;   NES*/

	font-weight: normal;
}

.largertypography.accordion .single .ac-toggle {
	position: relative;
	display: block;
	padding: 16px 0;
	padding-right: 40px;
	font-size: 18px;
	color: #111111;
	padding-left: 25px;
	padding-right: 50px;
	z-index: 1;
	border-bottom: 1px solid transparent;
}

.largertypography.accordion .single .ac-toggle:focus {
	text-decoration: none;
}

.largertypography.accordion .single .ac-toggle span {
	/*font-weight: bold; NES*/
	font-weight: normal;
	padding-right: 5px;
}

.largertypography.accordion .single .ac-toggle:hover {
	text-decoration: none;
}

.largertypography.accordion .single .ac-toggle.active {
	border-bottom: 1px solid #d8d8d8;
}

.largertypography.accordion .single .ac-toggle.active:after {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	transform: rotate(0deg);
}

.largertypography.accordion .single .ac-toggle:before {
	content: '';
	position: absolute;
	right: 20px;
	top: 50%;
	width: 12px;
	height: 2px;
	background: #d10074;
}

.largertypography.accordion .single .ac-toggle:after {
	content: '';
	position: absolute;
	right: 20px;
	top: 50%;
	width: 12px;
	height: 2px;
	background: #d10074;
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	transform: rotate(90deg);
	-webkit-transition: -webkit-transform 0.3s ease;
	transition: -webkit-transform 0.3s ease;
	-o-transition: transform 0.3s ease;
	transition: transform 0.3s ease;
	transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}

@media print {
	.largertypography .accordion .single .inner {
		display: block !important;
		padding-left: 0;
	}
	.largertypography .accordion .single .ac-toggle,
	.largertypography .accordion .single .ac-toggle span {
		padding-top: 40px !important;
		padding-bottom: 0 !important;
		padding-left: 0 !important;
		font-size: 24px !important;
		text-decoration: none;
		border-bottom: 0 !important;
	}
	.largertypography .accordion .single:first-child {
		border-top: 0;
	}
	.largertypography .accordion.result-page .single .ac-toggle {
		padding-left: 40px !important;
		padding-top: 20px !important;
	}
	.largertypography .accordion.result-page .single .ac-toggle:before {
		left: 0;
		top: 25px;
		background: none;
		content: '✓';
		color: #438516;
	}
	.largertypography .accordion.result-page .single .ac-toggle:after {
		display: none;
	}
	.largertypography .accordion.result-page .single .inner {
		padding-left: 40px !important;
	}
	.largertypography .accordion.result-page .single.fail .ac-toggle:before {
		left: 0;
		top: 25px;
		background: none;
		color: #b44113;
		content: '!';
	}
}

.largertypography .helper-box {
	position: relative;
	background: rgba(67, 133, 22, 0.1);
	margin-bottom: 20px;
}

.largertypography .helper-box ul {
	margin-bottom: 0;
}

.largertypography .helper-box li:before {
	background: #438516 !important;
}

.largertypography .helper-box.warning {
	background: rgba(214, 63, 2, 0.1);
}

.largertypography .helper-box.warning li:before {
	background: #b44113 !important;
}

.largertypography .helper-box.warning h2 {
	background: #b44113;
	margin-bottom: 0;
}

.largertypography .helper-box h2 {
	font-size: 21px;
	/*font-weight: bold; NES*/
	font-weight: normal;
	display: block;
	background: #438516;
	padding: 15px 15px;
	color: #fff;
	margin-bottom: 0;
}

.largertypography .helper-box .content {
	padding: 20px 15px;
	margin: 0;
}

.largertypography .helper-box .content h6 {
	padding-bottom: 5px;
}

@media screen and (min-width: 768px) {
	.largertypography .helper-box {
		margin-bottom: 50px;
	}
	.largertypography .helper-box h2 {
		padding: 15px 30px;
		font-size: 24px;
	}
	.largertypography .helper-box .content {
		padding: 30px 30px 25px;
	}
}

.largertypography.helper-box {
	position: relative;
	background: rgba(67, 133, 22, 0.1);
	margin-bottom: 20px;
}

.largertypography.helper-box ul {
	margin-bottom: 0;
}

.largertypography.helper-box li:before {
	background: #438516 !important;
}

.largertypography.helper-box.warning {
	background: rgba(214, 63, 2, 0.1);
}

.largertypography.helper-box.warning li:before {
	background: #b44113 !important;
}

.largertypography.helper-box.warning h2 {
	background: #b44113;
	margin-bottom: 0;
}

.largertypography.helper-box h2 {
	font-size: 21px;
	font-weight: normal;
	display: block;
	background: #438516;
	padding: 15px 15px;
	color: #fff;
	margin-bottom: 0;
}

.largertypography.helper-box .content {
	padding: 20px 15px;
	margin: 0;
}

.largertypography.helper-box .content h6 {
	padding-bottom: 5px;
}

@media screen and (min-width: 768px) {
	.largertypography.helper-box {
		margin-bottom: 50px;
	}
	.largertypography.helper-box h2 {
		padding: 15px 30px;
		font-size: 24px;
	}
	.largertypography.helper-box .content {
		padding: 30px 30px 25px;
	}
}

@media print {
	.largertypography.helper-box {
		border-left: 16px solid #438516;
	}
	.largertypography.helper-box.warning {
		border-left: 16px solid #b44113;
	}
}

@media (max-width: 576px) {
	.largertypography.result-page .banner.text .text-between .btn-ghost {
		margin-bottom: 20px;
	}
}

.largertypography .banner.text {
	position: relative;
	padding: 5px 0;
}

.largertypography .banner.text.mpb-20 {
	padding-bottom: 20px;
}

.largertypography .banner.text .text-between .btn-ghost {
	margin-top: 0;
}

.largertypography .banner.text.mpb-40 {
	padding-bottom: 40px;
}

.largertypography .banner.text.pt-0 {
	padding-top: 0;
}

.largertypography .banner.text.pb-0 {
	padding-bottom: 0;
}

@media screen and (min-width: 991px) {
	.largertypography .banner.text .button-right .text-between {
		text-align: right;
	}
}

.largertypography .banner.text.nomargin .title {
	margin-bottom: 0;
	padding-bottom: 0;
}

.largertypography .banner.text .title,
.largertypography .banner.text h1 {
	margin: 0;
	padding-bottom: 25px;
}

.largertypography .banner.text .title.small-padding {
	padding-bottom: 10px;
}

@media screen and (min-width: 768px) {
	.largertypography .banner.text .title,
	.largertypography .banner.text h1 {
		padding-bottom: 35px;
	}
}

.largertypography .banner.text > a:not(.btn) {
	color: #d10074;
	font-size: 18px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	/*font-weight: bold; NES*/
	font-weight: normal;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.largertypography .banner.text > a:not(.btn):hover span {
	color: #d10074 !important;
	border: none;
}

.largertypography .banner.text > a:not(.btn) img {
	margin-right: 5px;
	width: 20px;
}

.largertypography .banner.text > a:not(.btn) i {
	margin-right: 5px;
}

.largertypography .banner.text > a:not(.btn):hover,
.largertypography .banner.text > a:not(.btn):active,
.largertypography .banner.text > a:not(.btn):focus,
.largertypography .banner.text > a:not(.btn).active {
	text-decoration: none;
}

.largertypography .banner.text > a:not(.btn) span {
	border-bottom: 1px solid #d10074;
	line-height: 1;
}

@media screen and (min-width: 768px) {
	.largertypography .banner.text {
		padding: 0px 0 0px;
	}
	.largertypography .banner.text.pb-0 {
		padding: 20px 0 0px;
	}
}

.banner.text.largertypography {
	position: relative;
	padding: 5px 0;
}

.banner.text.largertypography.mpb-20 {
	padding-bottom: 20px;
}

.banner.text.largertypography .text-between .btn-ghost {
	margin-top: 0;
}

.banner.text.largertypography.mpb-40 {
	padding-bottom: 40px;
}

.banner.text.largertypography.pt-0 {
	padding-top: 0;
}

.banner.text.largertypography.pb-0 {
	padding-bottom: 0;
}

@media screen and (min-width: 991px) {
	.banner.text.largertypography .button-right .text-between {
		text-align: right;
	}
}

.banner.text.largertypography.nomargin .title {
	margin-bottom: 0;
	padding-bottom: 0;
}

.banner.text.largertypography .title {
	margin: 0;
	padding-bottom: 25px;
}

.banner.text.largertypography .title.small-padding {
	padding-bottom: 10px;
}

@media screen and (min-width: 768px) {
	.banner.text.largertypography .title {
		padding-bottom: 35px;
	}
}

.banner.text.largertypography > a:not(.btn) {
	color: #d10074;
	font-size: 18px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	/*font-weight: bold; NES*/
	font-weight: normal;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.banner.text.largertypography > a:not(.btn):hover span {
	color: #d10074 !important;
	border: none;
}

.banner.text.largertypography > a:not(.btn) img {
	margin-right: 5px;
	width: 20px;
}

.banner.text.largertypography > a:not(.btn) i {
	margin-right: 5px;
}

.banner.text.largertypography > a:not(.btn):hover,
.banner.text.largertypography > a:not(.btn):active,
.banner.text.largertypography > a:not(.btn):focus,
.banner.text.largertypography > a:not(.btn).active {
	text-decoration: none;
}

.banner.text.largertypography > a:not(.btn) span {
	border-bottom: 1px solid #d10074;
	line-height: 1;
}

@media screen and (min-width: 768px) {
	.banner.text.largertypography {
		padding: 40px 0 20px;
	}
	.banner.text.largertypography.pb-0 {
		padding: 40px 0 0px;
	}
}

@media print {
	.largertypography .banner .banner-image {
		height: auto !important;
		display: block !important;
		background: none !important;
		padding-bottom: 0 !important;
	}
	.largertypography .banner .banner-content {
		margin: 0;
	}
	.largertypography .banner .text .mpb-40 {
		padding-bottom: 0 !important;
	}
	.largertypography .banner.text > a:not(.btn) {
		padding-left: 0 !important;
	}
	.largertypography .banner.text > a:not(.btn) span {
		border-bottom: 0 !important;
	}
	.largertypography .banner.text > a:not(.btn) img {
		display: none !important;
	}
	.largertypography .banner.text .text-between .btn-ghost {
		display: none !important;
	}
}

.largertypography .larger-progress-bar {
	position: relative;
	margin-bottom: 30px;
}

@media screen and (min-width: 768px) {
	.largertypography .larger-progress-bar {
		margin-bottom: 30px;
		margin-top: 15px;
	}
}

.largertypography .larger-progress-bar h6 {
	font-size: 16px;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

.largertypography .larger-progress-bar .bar-holder {
	position: relative;
	display: block;
	height: 9px;
	width: 100%;
	border-radius: 30px;
	background: #d8d8d8;
}

.largertypography .larger-progress-bar .bar-holder .progress {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	border-radius: 30px;
	background: #438516;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
}

.largertypography .larger-progress-bar .bar-holder .progress.complete {
	border-top-right-radius: 30px;
	border-bottom-right-radius: 30px;
	width: 100%;
}

@media print {
	.largertypography .larger-progress-bar {
		display: none !important;
	}
}

.largertypography .serverform {
	margin-bottom: 0px;
}

@media screen and (min-width: 991px) {
	.largertypography .serverform {
		margin-bottom: 40px;
	}
}

.largertypography .form-group {
	position: relative;
	margin-bottom: 35px;
	padding-top: 30px;
}

@media screen and (min-width: 678px) {
	.largertypography .form-group {
		margin-bottom: 55px;
	}
}

.largertypography .checkbox {
	position: relative;
	margin-bottom: 15px;
}

.largertypography .checkbox input[type='checkbox'] {
	display: none;
}

.largertypography .checkbox input[type='checkbox']:checked ~ label {
	color: #fff;
	background: #00838f;
	border-color: #00838f;
}

.largertypography .checkbox input[type='checkbox']:checked ~ label:after {
	display: block;
}

.largertypography .checkbox label {
	position: relative;
	-webkit-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	width: 100%;
	min-height: 40px;
	background: #eff0f0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 10px;
	/*font-weight: bold; NES */
	font-weight: normal;
	border: 1px solid #d1d3d4;
	padding-left: 40px;
	font-size: 18px;
}

.largertypography .checkbox label:before {
	content: '';
	position: absolute;
	left: 15px;
	top: 16px;
	width: 14px;
	height: 14px;
	border: 1px solid #666d70;
	background: -webkit-gradient(linear, left bottom, left top, from(#dadada), to(#ffffff));
	background: -o-linear-gradient(bottom, #dadada, #ffffff);
	background: linear-gradient(to top, #dadada, #ffffff);
}

.largertypography .checkbox label:after {
	content: '';
	position: absolute;
	left: 17px;
	top: 19px;
	background: url('/static/img/checkbox.svg') center center no-repeat;
	background-size: 10px 8px;
	width: 10px;
	height: 8px;
	font-size: 12px;
	display: none;
	color: #111;
}

.largertypography .check-list {
	padding-bottom: 20px;
}

.largertypography .check-list ul {
	padding: 0;
	margin: 0;
	list-style: none;
}

.largertypography .check-list li {
	position: relative;
	padding: 20px;
	padding-left: 70px;
	border-top: 2px solid #d8d8d8;
}

.largertypography .check-list li:last-child {
	border-bottom: 2px solid #d8d8d8;
}

.largertypography .check-list li h3 {
	padding-bottom: 5px;
}

.largertypography .check-list li p {
	margin-bottom: 0 !important;
	padding-bottom: 0 !important;
}

.largertypography .check-list li blockquote {
	position: relative;
	padding: 0;
	padding-left: 40px;
	margin-top: 20px;
}

.largertypography .check-list li blockquote:before {
	content: '';
	position: absolute;
	left: 0;
	top: 0;
	width: 16px;
	height: calc(100% - 5px);
	background: #b44113;
}

.largertypography .check-list li:before {
	content: '';
	position: absolute;
	left: 20px;
	top: 25px;
	background: url('/static/img/check.svg') center center no-repeat;
	background-size: contain;
	width: 25px;
	height: 18px;
}

.largertypography .check-list li.fail {
	background: #faebe5;
	border-width: 1px;
}

.largertypography .check-list li.fail:before {
	content: '';
	width: 25px;
	height: 25px;
	top: 20px;
	background: url('/static/img/info-red.svg') center center no-repeat;
	background-size: 25px 25px;
}

.largertypography .check-list li.warning {
	background: #fffbcc;
	border-width: 1px;
}

.largertypography .check-list li.warning:before {
	content: '';
	width: 25px;
	height: 25px;
	top: 20px;
	background: url('/static/img/info-warning.svg') center center no-repeat;
	background-size: 25px 25px;
}

@media (max-width: 768px) {
	.largertypography .check-list li {
		padding-left: 40px;
	}
	.largertypography .check-list li:before {
		left: 5px;
	}
}

.largertypography .stack-title-block {
	position: relative;
}

.largertypography .stack-title-block .responsive-title {
	font-size: 21px;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

.largertypography .stack-title-block .responsive-title:first-child {
	margin-bottom: 20px;
	display: block !important;
}

.largertypography .table-responsive-stack {
	border: 0 !important;
}

.largertypography .table-responsive {
	border: 0;
}

.largertypography .table-responsive-stack {
	font-size: 12px;
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .table-responsive-stack.three-column thead th:first-child {
	width: 70%;
}

.largertypography .table-responsive-stack span.small {
	display: block;
	font-size: 12px;
	color: #6d6d6d;
	/*font-weight: 600 !important; NES*/
	font-weight: normal;
}

.largertypography .table-responsive-stack th {
	/*font-weight: 500; NES*/
	font-weight: normal;
}

.largertypography .table-responsive-stack th,
.largertypography .table-responsive-stack td {
	border-top: 1px solid #d8d8d8;
}

.largertypography .table-responsive-stack thead tr {
	border-top: 1px solid #d8d8d8;
}

.largertypography .table-responsive-stack thead th {
	/*font-weight: bold; NES*/
	font-weight: normal;

	border-bottom: 0 !important;
	border-right: 1px solid #d8d8d8;
	border-top: 2 solid #d8d8d8;
	vertical-align: top;
	background: #fff;
	color: #111;
	font-size: 16px;
	padding-top: 20px !important;
	padding-bottom: 20px !important;
	text-align: center;
}

.largertypography .table-responsive-stack thead th:first-child {
	padding-left: 0;
	text-align: left;
	width: 50%;
}

.largertypography .table-responsive-stack thead th:last-child {
	border-right: 0;
}

.largertypography .table-responsive-stack tbody tr:last-child {
	border-bottom: 1px solid #d8d8d8;
}

.largertypography .table-responsive-stack tbody tr td {
	text-align: center;
	padding: 30px 10px;
	font-size: 16px;
	border-right: 1px solid #d8d8d8;
}

.largertypography .table-responsive-stack tbody tr td a {
	font-weight: bold;
}

.largertypography .table-responsive-stack tbody tr td .append-list {
	padding-top: 20px;
	text-align: left;
}

.largertypography .table-responsive-stack tbody tr td .append-list h4 {
	font-size: 21px;
	text-align: left;
}

.largertypography .table-responsive-stack tbody tr td .append-list span {
	display: block;
	padding-top: 5px;
}

@media screen and (min-width: 991px) {
	.largertypography .table-responsive-stack tbody tr td .append-list span {
		padding-top: 0;
	}
}

.largertypography .table-responsive-stack tbody tr td.bg-green {
	background: rgba(67, 133, 22, 0.1);
}

.largertypography .table-responsive-stack tbody tr td .td-content {
	min-height: 50px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
}

.largertypography .table-responsive-stack tbody tr td span.text {
	font-size: 16px;
	color: #438516;
	/*font-weight: bold; NES*/

	font-weight: normal;
}

.largertypography .table-responsive-stack tbody tr td span.text-center {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.largertypography .table-responsive-stack tbody tr td img {
	margin-left: 0;
	margin-right: 0;
	margin-bottom: 10px;
}

.largertypography .table-responsive-stack tbody tr td:first-child {
	text-align: left;
	font-size: 18px;
	padding-top: 20px;
	padding-bottom: 20px;
	padding-left: 0;
	color: #111 !important;
	font-weight: normal;
}

.largertypography .table-responsive-stack tbody tr td:last-child {
	border-right: 0;
}

@media (max-width: 991px) {
	.largertypography .table-responsive-stack tr {
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		display: block;
	}
	.largertypography .table-responsive-stack td,
	.largertypography .table-responsive-stack th {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-flex: 1;
		-ms-flex: 1 1 auto;
		flex: 1 1 auto;
		border-right: 0;
	}
	.largertypography .table-responsive-stack tr {
		border-top: 1px solid #d8d8d8;
		border-bottom: 1px solid #d8d8d8;
		margin-bottom: 30px;
	}
	.largertypography .table-responsive-stack tr td {
		white-space: normal !important;
		border-right: 0 !important;
		min-height: 50px;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		padding: 5px 0 !important;
	}
	.largertypography .table-responsive-stack tr td:first-child {
		border-top: 3px solid #d8d8d8;
	}
	.largertypography .table-responsive-stack tr td:first-child .text-right {
		text-align: left;
		padding: 10px 0;
	}
	.largertypography .table-responsive-stack tr td .text-right {
		-webkit-box-flex: 40%;
		-ms-flex: 40%;
		flex: 40%;
	}
	.largertypography .table-responsive-stack tr td.bg-green {
		background: transparent !important;
	}
	.largertypography .table-responsive-stack tr td.bg-green .td-content {
		background: rgba(67, 133, 22, 0.1);
		min-width: 100px;
		/* NES - PSP squishing against dot after removing .text-right */
	}
	.largertypography .table-responsive-stack tr td .td-content {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: row-reverse !important;
		flex-direction: row-reverse !important;
		min-height: auto;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
		padding: 10px 15px;
	}
	.largertypography .table-responsive-stack tr td .td-content img {
		width: 20px;
		margin-bottom: 0;
	}
	.largertypography .table-responsive-stack .stacked-title,
	.largertypography .table-responsive-stack .stacked-heading {
		-ms-flex-preferred-size: 60%;
		flex-basis: 60%;
		text-align: left !important;
		/*font-weight: 700; NES*/

		/*NES*/
		font-weight: normal;
	}
	.largertypography .table-responsive-stack .text-right {
		-webkit-box-flex: 40%;
		-ms-flex: 40%;
		flex: 40%;
	}
	.largertypography .table-responsive-stack td:first-child .stacked-heading {
		display: none !important;
	}
}

@media (max-width: 576px) {
	.largertypography .table-responsive-stack tr {
		margin-bottom: 0;
	}
}

@media print {
	.largertypography .stack-title-block {
		display: none !important;
	}
	.largertypography .table-responsive-stack {
		margin-bottom: 0 !important;
	}
	.largertypography .table-responsive-stack td {
		border-top: 0 !important;
		border-left: 1px solid #d8d8d8;
	}
	.largertypography .table-responsive-stack tr {
		border-bottom: 0 !important;
	}
	.largertypography .table-responsive-stack tbody tr {
		border-bottom: 0 !important;
		margin-bottom: 0 !important;
		clear: both !important;
	}
	.largertypography .table-responsive-stack tbody tr .stacked-heading {
		display: none !important;
	}
	.largertypography .table-responsive-stack tbody tr td {
		border-left: 1px solid #d8d8d8;
		width: 15% !important;
		display: table-cell !important;
		border-bottom: 1px solid #d8d8d8 !important;
	}
	.largertypography .table-responsive-stack tbody tr td span.text {
		display: block;
		color: #438516 !important;
	}
	.largertypography .table-responsive-stack tbody tr td img {
		width: 24px;
	}
	.largertypography .table-responsive-stack tbody tr td:first-child {
		border-top: 0 !important;
		width: 70% !important;
		border-left: 0;
	}
	.largertypography .table-responsive-stack tbody tr td .td-content {
		display: block !important;
		text-align: center !important;
	}
	.largertypography .table-responsive-stack thead {
		display: block !important;
	}
	.largertypography .table-responsive-stack thead tr {
		margin-bottom: 0 !important;
	}
	.largertypography .table-responsive-stack thead th {
		width: 15% !important;
		display: table-cell !important;
	}
	.largertypography .table-responsive-stack thead th:first-child {
		width: 70% !important;
	}
	.largertypography .table-responsive-stack.four thead th:first-child,
	.largertypography .table-responsive-stack.four tbody tr td:first-child {
		width: 55% !important;
	}
}

.largertypography.blueprint-content {
	padding-top: 0 !important;
}

.largertypography.blueprint-content.pdf .table-responsive {
	margin-bottom: 60px;
}

.largertypography .blueprint.container {
	padding-bottom: 0;
}

@media (max-width: 991px) {
	.largertypography .blueprint .banner.text {
		padding-top: 0;
	}
}

.largertypography .blueprint .col-md-12 > .td-content {
	display: none !important;
}

.largertypography .blueprint .header {
	border-bottom: 0;
}

.largertypography .blueprint .header .logo {
	margin-left: 0;
	margin-bottom: 0;
}

@media (min-width: 991px) {
	.largertypography .blueprint .header .logo {
		margin-top: 40px;
	}
}

.largertypography .blueprint .header .row {
	max-width: 1140px !important;
}

@media (max-width: 991px) {
	.largertypography .blueprint .header {
		position: relative;
	}
	.largertypography .blueprint .header .container {
		padding-top: 25px;
	}
}

.largertypography .pdf-description {
	position: relative;
	padding: 30px 0 30px;
	border-top: 2px solid #d8d8d8;
}

.largertypography .pdf-description h3 {
	font-size: 24px;
	padding-bottom: 5px;
}

.breadcrumb-tab {
	position: relative;
	background: #eff0f0;
}

.breadcrumb-tab a {
	position: relative;
	/*padding: 16px 0;*/
	font-size: 14px;
	font-weight: bold;
	color: #00334c;
	display: block;
}

.breadcrumb-tab a:hover {
	text-decoration: none;
}

.breadcrumb-tab a {
	background: url('/static/img/chevron-right.svg') 0 17px no-repeat;
	background-size: 7px 9px;
	padding: 13px 0 13px 20px;
}

@media screen and (min-width: 991px) {
	.breadcrumb-tab a {
		display: none;
	}
}

@media screen and (min-width: 520px) {
	.breadcrumb-tab a {
		/*padding: 13px 0;*/
		font-size: 16px;
		background-size: 8px 11px;
		background-position-y: 18px;
		padding: 13px 0 13px 20px;
	}
}

@media print {
	.breadcrumb-tab a:before {
		display: none;
	}
}

.components > h2 {
	margin-bottom: 30px;
}

.components .mt-50 {
	margin-top: 50px;
}

.components .component-title {
	font-size: 12px;
	text-transform: uppercase;
	color: #666d70;
	display: block;
	margin-bottom: 10px;
	margin-top: 10px;
	/*font-weight: bold; NES*/
	font-weight: normal;
}

.search .form-group {
	padding-top: 20px !important;
}

.largertypography a.btn-primary {
	color: white !important;

	font-weight: normal;
}

.dcnav-main.largertypography {
	z-index: inherit;
}

.hide-unless-print {
	display: none !important;
}

@media print {
	* {
		-webkit-transition: none !important;
		-o-transition: none !important;
		transition: none !important;
	}
	.hide-unless-print {
		display: block !important;
	}
	.hide-print {
		display: none !important;
	}
	.breadcrumb-tab {
		display: none !important;
	}
}

/*--------------------------*/

/*  additional styles for multistep nav */

.largertypography .expand-lists .process-lists li.list-expanded a.showLink,
.largertypography .expand-lists .process-lists li a.hideLink {
	display: none;
}

.largertypography .expand-lists .process-lists li.list-expanded a.hideLink,
.largertypography .expand-lists .process-lists li a.showLink {
	display: inline;
	color: #d10074;
}

.largertypography .expand-lists .trigger-process-list:not(.small) a:not(.active) {
	display: none;
}

.largertypography .expand-lists .process-lists li.list-expanded .toggle-content {
	display: block;
}

.largertypography .expand-lists .trigger-process-list .show-mobile {
	display: none;
}

/*-------------------------*/

main.main.largertypography
	section
	ul.process-lists.small
	li.active.list-expanded
	div.list-wrapper
	div.toggle-content
	a:hover {
	text-decoration: none;
}

/*main section div.row div div.sidebar div.toggle-content a.special-link:visited span,*/

main.main.largertypography section div.row div div.sidebar div.toggle-content a.special-link span,
main.main.largertypography section div.row div div.sidebar div.toggle-content a.special-link:hover span {
	text-decoration: none;

	font-weight: normal;
	color: #d10074;
	/*#111111;*/
}

#SecondaryNavigationHeading {
	margin-top: 15px;
}

@media screen and (min-width: 991px) {
	.hub {
		padding-bottom: 32px;
	}
	div.hub div.row div {
		padding-bottom: 13px;
	}
	/*move toolbox up on lading page*/
	div.learn-more.largertypography > div.module.hub.learn-more {
		margin-bottom: -40px;
		padding-bottom: 0px;
	}
}

@media screen and (min-width: 768px) {
	.learn-more.largertypography,
	.module.share {
		/*margin-left: 10px;*/
		padding-left: 0px;
	}
}

div.hub.how-to-register > div.media {
	margin-bottom: 0px;
}

.largertypography div.hub.how-to-register {
	margin-bottom: 0px;
}

@media screen and (max-width: 991px) {
	.overseas-landing-intro p.lead {
		/*margin-left: 15px;*/
	}
}

.toggle-content a,
.largertypography .expand-lists .process-lists li div.toggle-content > p > span {
	font-weight: normal;
	max-width: 100%;
	display: block;
}

h2.lead {
	margin-top: -20px;
	color: #666d70;
	/*font-family: 'Foundry Monoline W01 Md', arial, sans-serif; NES*/

	/*font-weight: bold; NES*/
	font-weight: normal;
	font-size: 24px;
}

.largertypography div.module.share ul.list-group {
	/*margin-right:15px;*/
}

.largertypography div.row section div.col-md-8.module.share {
	margin-right: -15px;
}

@media screen and (max-width: 769px) {
	.largertypography div.row section div.col-md-8.module.share {
		margin-left: -10px;
	}
}

div.information-block {
	position: relative;
	margin-bottom: 15px;
}

div.information-block h3 {
	margin-left: 45px;
	margin-bottom: 20px;
}

div.information-block h3:before {
	content: '';
	position: absolute;
	background: url('/static/img/info-black.svg') center center no-repeat;
	left: 0px;
	top: -4px;
	width: 32px;
	height: 34px;
	display: block;
}

@media print {
	.largertypography div.expand-lists a.showLink,
	.largertypography div.expand-lists a.hideLink,
	.largertypography .expand-lists .trigger-process-list .show-mobile.active,
	.largertypography .expand-lists .process-lists li a.showLink,
	.largertypography .expand-lists .process-lists li.list-expanded a.hideLink {
		display: none;
	}
}

.largertypography .banner.text .box-description {
	font-size: 16px;
	position: relative;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 10px;
}

.largertypography .banner.text .box-description:last-child {
	margin-bottom: 20px;
}

.largertypography .banner.text .box-description span.box {
	position: relative;
	width: 43px;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 43px;
	flex: 0 0 43px;
	height: 31px;
	border: 1px solid #d1d3d4;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 16px;
	/*font-weight: bold; NES*/
	font-weight: normal;

	color: #438516;
	line-height: 1;
	margin-right: 10px;
}

@media screen and (min-width: 991px) {
	.largertypography .banner.text .button-right .text-between.alter {
		position: relative;
		margin-top: -60px;
	}
}

/* fixing js for blueprint page */

.stack-title-block {
	display: none;
}

@media screen and (max-width: 991px) {
	.stack-title-block {
		display: block;
	}
}

@media print {
	div.largertypography div.banner.text p.box-description span.text {
		color: #438516 !important;
	}
}

span.blueprint-itemtextbody {
	display: inline-block;
	margin-top: 15px;
	margin-bottom: 10px;
}

p.box-description span.text {
	background: rgba(67, 133, 22, 0.1);
}

/* override to make ordered lists match unordered lists */

.largertypography ol,
.largertypography ul:not(.breadcrumb):not(.print-toolbox) {
	padding-left: 15px;
	font-size: 18px;
}

.largertypography table ul li,
.largertypography table ol li,
.largertypography table ul li ul {
	font-size: 16px;
}

.largertypography .accordion .single .inner ul li,
.inner ol li {
	font-size: 18px;
}

.cta-bar .right ul li,
.cta-bar .right ol li {
	font-size: 14px;
}

.content-wrapper .largertypography table ul li,
.content-wrapper .largertypography table ol li,
.content-wrapper .largertypography table ul li ul {
	font-size: 16px;
}

.largertypography .content-wrapper .responsive ul:not(.process-lists):not(.check) li {
	font-size: 16px;
}

.largertypography .content-wrapper .cta-bar .right ul li {
	font-size: 14px;
}

.largertypography .content-wrapper .table-responsive .table ul li {
	font-size: 16px;
}

.largertypography .content-wrapper .table td p {
	font-size: 16px;
	display: block;
	color: #111;
}

.largertypography .table td p {
	font-size: 16px;
	display: block;
	color: #111;
}

.largertypography .breadcrumb ul {
	padding-left: 0 !important;
	font-size: 14px !important;
}

/*
      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        background: var(--page-bg);
        color: var(--text);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Inter, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif;
        line-height: 1.5;
      }

      .container { max-width: 1120px; margin-inline: auto; padding-inline: 1rem; }

      /* Utility top bar 
      .utility { background: #fff; border-bottom: 1px solid var(--border); font-size: .875rem; }
      .utility .row { display: flex; gap: 1rem; align-items: center; justify-content: space-between; padding: .5rem 0; }
      .util-links, .util-actions { display: flex; gap: 1rem; flex-wrap: wrap; }
      .utility a { color: #374151; text-decoration: none; }
      .utility a:hover, .utility a:focus { text-decoration: underline; }

      /* Brand bar 
      .brandbar { background: #fff; border-bottom: 1px solid var(--border); }
      .brandbar .row { display: flex; align-items: center; justify-content: space-between; gap: 1rem; padding: .75rem 0; }
      .brand { display: flex; align-items: center; gap: .75rem; }
      .brand .logo { height: 46px; width: auto; display: block; }
      .brand .name { font-weight: 700; font-size: 1.0625rem; letter-spacing: .2px; white-space: nowrap; color: #111827; }
      */
      /* Primary nav 
      .navbar { background: var(--nmc-purple); color: #fff; }
      .navbar nav { display: flex; gap: .25rem; overflow-x: auto; padding: .5rem 0; }
      .navbar a { color: #fff; text-decoration: none; font-weight: 600; padding: .5rem .75rem; border-radius: .5rem; }
      .navbar a:hover, .navbar a:focus { background: rgba(255,255,255,.12); }
*/
      /* Main layout
      main { padding: 1.5rem 0 2.5rem; }
      .grid { display: grid; grid-template-columns: 1fr 320px; gap: 2rem; align-items: start; }
      @media (max-width: 900px) { .grid { grid-template-columns: 1fr; } }
 */
      /* Left column 
      .breadcrumb { font-size: .875rem; color: #6b7280; margin-bottom: .75rem; }
      .breadcrumb a { color: #374151; text-decoration: none; }
      .breadcrumb a:hover { text-decoration: underline; }

      h1 { margin: 0 0 .25rem; font-size: clamp(1.6rem, 1.1rem + 1.6vw, 2.25rem); font-weight: 800; letter-spacing: -.01em; }
      .lead { color: var(--muted); margin-bottom: 1rem; }
      .panel { background: #fff; border: 1px solid rgba(10,34,57,.10); border-radius: 12px; box-shadow: 0 8px 30px rgba(10,34,57,.06); overflow: hidden; }
      .panel .body { padding: 1.25rem; }
      .btn { display: inline-flex; align-items: center; gap: .5rem; padding: .6rem .9rem; font-weight: 700; border-radius: .5rem; text-decoration: none; }
      .btn-primary { background: var(--nmc-pink); color: #fff; }
      .btn-primary:hover { filter: brightness(.95); }
*/
  
      .notice {
        /* background: #F0F7FA; */
        border: 1px solid color-mix(in oklab, var(--nmc-teal) 35%, white);
        color: #0b3a4d; padding: 1rem; border-radius: .5rem; margin: 1rem 0 0;
      }

           /*    
      .iwant { border: 1px solid var(--border); }
      .iwant .head { background: #e6ecff; color: #111827; padding: .9rem 1rem; border-bottom: 1px solid var(--border); font-weight: 800; }
      .iwant ul { list-style: none; padding: .5rem; margin: 0; display: grid; gap: .5rem; }
      .iwant a { display: flex; align-items: center; justify-content: space-between; text-decoration: none; padding: .9rem 1rem; border-radius: .5rem; background: #0b7aa61a; color: #0b4961; font-weight: 700; border: 1px solid #0b7aa62b; }
      .iwant a:hover { background: #0b7aa629; }

      .footer { background: var(--nmc-purple); color: #fff; padding: 2rem 0; }
      .footer .cols { display: grid; grid-template-columns: 1.2fr 1fr 1fr 1fr; gap: 1.5rem; }
      .footer h2 { font-size: clamp(1.5rem, 1.1rem + 1.5vw, 2rem); margin: 0 0 .5rem; }
      .footer .big { font-size: 38px; font-family:'Gotham bold', Arial, sans-serif; display: block; color:#ffffff;  font-weight: 900; line-height: 1.3; opacity: .95; }
      .footer a { color: #fff; text-decoration: none; }
      .footer .prose { font-weight: normal !important;margin-bottom: 0; margin: 0 0 15px; box-sizing: border-box; margin-bottom: 0; display: block; margin-block-start: 1em;  margin-block-end: 1em;  margin-inline-start: 0px; margin-inline-end: 0px; unicode-bidi: isolate;}
      .footer a:hover { text-decoration: underline; }
      .footer ul { list-style: none; padding: 0; margin: 0; display: grid; gap: .4rem; }
      @media (max-width: 900px) { .footer .cols { grid-template-columns: 1fr 1fr; } }
      @media (max-width: 560px) { .footer .cols { grid-template-columns: 1fr; } }
*/
      /* 
      .site-footer { background: var(--nmc-navy); color: #cbd5e1; }
      .site-footer .row { display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; gap: .75rem 1rem; padding: 1rem 0; }
      .foot-links, .legal { display: flex; flex-wrap: wrap; gap: .75rem 1rem; }
      .site-footer a { color: #e2e8f0; text-decoration: none; }
      .site-footer a:hover { text-decoration: underline; }
      .copy { opacity: .9; }
 */
      /* 
      .skip-link { position: absolute; left: -9999px; top: auto; width: 1px; height: 1px; overflow: hidden; }
      .skip-link:focus { left: 1rem; top: 1rem; width: auto; height: auto; padding: .5rem .75rem; background: #fff; color: #000; border-radius: .5rem; box-shadow: 0 2px 8px rgba(0,0,0,.15); }
     */
      </style>
</meta></meta><style id="notice-override">
/* Notice-only override: leaves all other site styles intact */
.notice{
  width:100%;
  max-width:640px;
  margin: 1.5rem auto;
  padding:2rem;
  background:#fffbe6;
  border:4px solid #ffd600;
  border-radius:24px;
  box-shadow:0 8px 40px rgba(0,0,0,.18), 0 2px 12px rgba(0,0,0,.12);
  text-align:center;
  font-size:1.125rem;
  font-weight:600;
  letter-spacing:.01em;
}
.notice h1,.notice h2{ 
  margin:0 0 .5rem 0; 
  font-size:clamp(1.5rem, 2vw + 1rem, 2rem);
}
.notice p{ margin:0 0 1rem 0; }
.notice ul{
  margin:0 auto 1rem;
  padding-left:1.25rem;
  text-align:left;
  display:inline-block;
}
.notice li{ margin:0 0 .5rem; }
.notice hr{
  margin:1.5rem 0;
  border:0;
  border-top:1px solid #d1d3d4;
}
@media (prefers-reduced-motion:no-preference){
  .notice{ transition: box-shadow .2s ease; }
  .notice:hover, .notice:focus-within{
    box-shadow:0 10px 50px rgba(0,0,0,.22), 0 3px 16px rgba(0,0,0,.14);
  }
}
</style></head>
<body>
<header class="header zindex-fix" role="banner">
<div class="container">
<div class="row">
<div class="logo">
<a class="removePrint" href="/" title="Return to homepage">
<img alt="The Nursing and Midwifery Council" src="https://www.nmc.org.uk/static/img/refresh/logo-color-stacked.svg"/>
</a>
</div>
<!--/.header-bar-->
<ul class="header-links">
<li>
<a href="https://www.nmc.org.uk/about-us/our-role/ein-rol/" hreflang="cy" lang="cy">
                    Cymraeg
                </a>
</li>
<li>
<a href="/concerns-nurses-midwives/hearings/hearings-sanctions/" target="_top" title="go to hearings">Latest hearings</a>
</li>
<li>
<a href="https://online.nmc-uk.org/Account/Login?ReturnUrl=/" target="_top" title="NMC Online">NMC Online</a>
</li>
<li>
<a href="https://www.nmc.org.uk/contact-us/">

                    

Contact us                </a>
</li>
</ul>
<!--/.header-links-->
<div class="header-feature">
<!-- <a role="menuitem" tabindex="-1" href="/registration/search-the-register/" class="header-register"> -->
<a class="header-register" href="https://www.nmc.org.uk/registration/search-the-register/">
<span>Search the register</span>
<span class="icon icon-24 desktop-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right.svg"/></span>
<span class="icon icon-24 mobile-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right-white.svg"/></span>
</a>
<!-- <a role="menuitem" tabindex="-1" href="/registration/employer-confirmations/" class="header-register"> -->
<a class="header-register" href="/registration/employer-confirmations/">
<span>Employer confirmations</span>
<span class="icon icon-24 desktop-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right.svg"/></span>
<span class="icon icon-24 mobile-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right-white.svg"/></span>
</a>
</div>
<!--/.header-feature-->
<div class="header-bar">
<a class="header-nav" data-nav="true" href="#footer">
<span>Menu</span>
<svg role="presentation"><use xlink:href="/static/img/sprite.svg#nav"></use></svg>
</a>
<a class="header-search" data-search="true" href="#search-criteria">
<span>Search</span>
<img alt="" class="header-search__img" src="https://www.nmc.org.uk/static/img/refresh/search-white.svg"/>
<img alt="" class="header-search__img header-search__img--active" src="https://www.nmc.org.uk/static/img/refresh/search.svg"/>
</a>
</div>
</div>
</div>
</header>
<!-- Main navigation -->
<nav class="dcnav" id="nav" role="navigation">
<div class="container dcnav-wrapper">
<h2 class="sr-only">Menu</h2>
<ul aria-label="Main Navigation" class="dcnav-menu" role="menubar">
<li class="dcnav-item dcnav-feature" role="none">
<!-- <a role="menuitem" tabindex="-1" href="/registration/search-the-register/" class=""> -->
<a class="" href="https://www.nmc.org.uk/registration/search-the-register/">
<span>Search the register</span>
<span class="icon icon-24 desktop-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right.svg"/></span>
<span class="icon icon-24 mobile-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right-white.svg"/></span>
</a>
</li>
<li class="dcnav-item dcnav-feature" role="none">
<!-- <a role="menuitem" tabindex="-1" href="/registration/employer-confirmations/" class=""> -->
<a class="" href="https://www.nmc.org.uk/registration/employer-confirmations/">
<span>Employer confirmations</span>
<span class="icon icon-24 desktop-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right.svg"/></span>
<span class="icon icon-24 mobile-inline-block"><img alt="" src="https://www.nmc.org.uk/static/img/refresh/arrow-right-white.svg"/></span>
</a>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="https://www.nmc.org.uk/about-us/" role="menuitem" tabindex="0">
                                About us
                                <button aria-expanded="false" aria-label="Open About us dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/" role="menuitem" tabindex="-1">About us</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/our-role/" role="menuitem" tabindex="0">What we do</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/governance/" role="menuitem" tabindex="0">Governance</a>
</li>
<li class="dcnav-item is-ajax" data-id="*********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/reports-and-accounts/" role="menuitem" tabindex="0">Reports and accounts</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/nmc-culture/" role="menuitem" tabindex="0">Culture Transformation Plan</a>
</li>
<li class="dcnav-item is-ajax" data-id="*********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/nmc-insights/" role="menuitem" tabindex="0">NMC Insights</a>
</li>
<li class="dcnav-item is-ajax" data-id="*********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.ukhttps://nmc.pinpointhq.com/" role="menuitem" tabindex="0">Careers</a>
</li>
<li class="dcnav-item is-ajax" data-id="*********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/consultations/" role="menuitem" tabindex="0">Consultations</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/policy/" role="menuitem" tabindex="0">Policy</a>
</li>
<li class="dcnav-item is-ajax" data-id="1907028909" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/equality-diversity-and-inclusion/" role="menuitem" tabindex="0">Equality, diversity and inclusion</a>
</li>
<li class="dcnav-item is-ajax" data-id="211312694" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/about-us/working-with-others/" role="menuitem" tabindex="0">Working with others</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="https://www.nmc.org.uk/standards/" role="menuitem" tabindex="0">
                                Standards
                                <button aria-expanded="false" aria-label="Open Standards dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/" role="menuitem" tabindex="-1">Standards</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item is-ajax" data-id="603247195" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/code/" role="menuitem" tabindex="0">The Code</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/revalidation/" role="menuitem" tabindex="0">Revalidation</a>
</li>
<li class="dcnav-item is-ajax" data-id="40621399" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/standards-for-nurses/" role="menuitem" tabindex="0">Standards for nurses</a>
</li>
<li class="dcnav-item is-ajax" data-id="1620651572" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/standards-for-midwives/" role="menuitem" tabindex="0">Standards for midwives</a>
</li>
<li class="dcnav-item is-ajax" data-id="1619662025" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/standards-for-nursing-associates/" role="menuitem" tabindex="0">Standards for nursing associates</a>
</li>
<li class="dcnav-item is-ajax" data-id="1053198097" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/standards-for-post-registration/" role="menuitem" tabindex="0">Standards for post registration</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/how-we-develop-our-standards/" role="menuitem" tabindex="0">How we develop our standards</a>
</li>
<li class="dcnav-item is-ajax" data-id="1306644468" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/guidance/" role="menuitem" tabindex="0">Guidance and Supporting Information</a>
</li>
<li class="dcnav-item is-ajax" data-id="176402634" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/standards/future-standards/" role="menuitem" tabindex="0">Future standards</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="/education/" role="menuitem" tabindex="0">
                                Education
                                <button aria-expanded="false" aria-label="Open Education dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/" role="menuitem" tabindex="-1">Education</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/approved-programmes/" role="menuitem" tabindex="0">Approved programmes</a>
</li>
<li class="dcnav-item is-ajax" data-id="1414534190" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/our-role-in-education/" role="menuitem" tabindex="0">Our role in education</a>
</li>
<li class="dcnav-item is-ajax" data-id="681775633" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/quality-assurance-of-education/" role="menuitem" tabindex="0">Quality assurance of education</a>
</li>
<li class="dcnav-item is-ajax" data-id="1321353448" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/developing-our-education-requirements/" role="menuitem" tabindex="0">Developing our education requirements</a>
</li>
<li class="dcnav-item is-ajax" data-id="1868075647" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/becoming-a-nurse-midwife-nursing-associate/" role="menuitem" tabindex="0">Becoming a nurse, midwife or nursing associate</a>
</li>
<li class="dcnav-item is-ajax" data-id="288045474" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/lead-midwifery-educators/" role="menuitem" tabindex="0">Contacts at education institutions</a>
</li>
<li class="dcnav-item is-ajax" data-id="822638828" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/education/information-for-students/" role="menuitem" tabindex="0">Resources for students</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="/concerns-nurses-midwives/" role="menuitem" tabindex="0">
                                Concerns
                                <button aria-expanded="false" aria-label="Open Concerns dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/" role="menuitem" tabindex="-1">Concerns</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/what-is-fitness-to-practise/" role="menuitem" tabindex="0">What is fitness to practise?</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/support-for-patients-families-and-public/" role="menuitem" tabindex="0">Information for patients, families and the public</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/information-for-registrants/" role="menuitem" tabindex="0">Information for people on our register</a>
</li>
<li class="dcnav-item is-ajax" data-id="**********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/information-for-witnesses/" role="menuitem" tabindex="0">Information for witnesses</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/employer-resource/" role="menuitem" tabindex="0">Managing Concerns: A resource for employers</a>
</li>
<li class="dcnav-item is-ajax" data-id="610838445" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/hearings/" role="menuitem" tabindex="0">Hearings</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/our-plan-for-fitness-to-practise/" role="menuitem" tabindex="0">Our plan for fitness to practise</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/ftp-library/" role="menuitem" tabindex="0">Fitness to Practise  library</a>
</li>
<li class="dcnav-item is-ajax" data-id="1016782463" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/raise-a-concern/" role="menuitem" tabindex="0">Raise a concern</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="/registration/" role="menuitem" tabindex="0">
                                Registration
                                <button aria-expanded="false" aria-label="Open Registration dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/" role="menuitem" tabindex="-1">Registration</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item is-ajax" data-id="390662335" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/search-the-register/" role="menuitem" tabindex="0">Search the register</a>
</li>
<li class="dcnav-item is-ajax" data-id="1715768901" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/employer-confirmations/" role="menuitem" tabindex="0">Employer confirmations</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/nmc-online/" role="menuitem" tabindex="0">NMC Online</a>
</li>
<li class="dcnav-item is-ajax" data-id="1236117420" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/joining-the-register/" role="menuitem" tabindex="0">Joining the register</a>
</li>
<li class="dcnav-item is-ajax" data-id="773790096" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/your-registration/" role="menuitem" tabindex="0">Manage your registration</a>
</li>
<li class="dcnav-item is-ajax" data-id="933631416" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/returning-to-the-register/" role="menuitem" tabindex="0">Returning to the register</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/information-for-internationally-trained-applicants/" role="menuitem" tabindex="0">Information for internationally trained applicants</a>
</li>
<li class="dcnav-item is-ajax" data-id="2068432479" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/registration-appeals/" role="menuitem" tabindex="0">Registration appeals</a>
</li>
<li class="dcnav-item is-ajax" data-id="1378754639" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/registration/guidance-for-employers/" role="menuitem" tabindex="0">Guidance for employers</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item" role="none">
<a aria-expanded="false" aria-haspopup="true" class="dcnav-link" href="https://www.nmc.org.uk/news/" role="menuitem" tabindex="0">
                                News
                                <button aria-expanded="false" aria-label="Open News dropdown menu" class="drop" type="button"></button>
</a>
<ul class="dcnav-menu">
<li class="dcnav-item" role="none">
<button class="dcnav-link dcnav-back" type="button">Back</button>
<h3 class="dcnav-title">
<a class="dcnav-link" href="https://www.nmc.org.uk/news/" role="menuitem" tabindex="-1">News</a>
</h3>
</li>
<!-- Sub children start -->
<li class="dcnav-item is-ajax" data-id="1615823893" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/news/news-and-updates/" role="menuitem" tabindex="0">News and updates</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/news/events/" role="menuitem" tabindex="0">Events</a>
</li>
<li class="dcnav-item" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/news/media-enquiries/" role="menuitem" tabindex="0">Media enquiries</a>
</li>
<li class="dcnav-item is-ajax" data-id="12********" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/news/email-newsletters/" role="menuitem" tabindex="0">Email newsletters</a>
</li>
<!-- Sub children end -->
</ul>
</li>
<li class="dcnav-item utility-mobile" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/concerns-nurses-midwives/hearings/hearings-sanctions/" role="menuitem" tabindex="-1">Latest hearings</a>
</li>
<li class="dcnav-item utility-mobile" role="none">
<a class="dcnav-link" href="https://online.nmc-uk.org/Account/Login?ReturnUrl=/" role="menuitem" tabindex="-1">NMC Online</a>
</li>
<li class="dcnav-item utility-mobile" role="none">
<a class="dcnav-link" href="https://www.nmc.org.uk/contact-us/" role="menuitem" tabindex="-1">Contact us</a>
</li>
</ul>
</div>
<button class="dcnav-close">
<span>Close Menu</span>
<svg role="presentation"><use xlink:href="https://www.nmc.org.uk/static/img/sprite.svg#close"></use></svg>
</button>
</nav>
<!-- Main -->
<main class="container" id="main" role="main">
<div class="breadcrumb">
</div>
<div class="grid">
<section>
<div class="panel">
<div class="body">
<div aria-live="polite" class="notice" role="status">
<h2 align="center"><strong>Maintenance notice</strong> </h2>
<p align="center">We apologise for any inconvenience, we are performing maintenance to our portals at which time myNMC and our other online services will be unavailable. Please check back again later.</p>
</div>
</div>
</div>
</section>
</div>
</main>
<footer class="footer" id="footer" role="contentinfo">
<div class="container">
<div class="row">
<div class="col-md-12">
<div class="footer-text">
<span class="footer-text__item footer-text__item--1">Regulate</span>
<span class="footer-text__item footer-text__item--2">Support</span>
<span class="footer-text__item footer-text__item--3">Influence</span>
</div>
<div class="footer-prose">
<p>We're the independent regulator of more than 853,000 nurses, midwives and nursing associates. Our vision is safe, effective and kind nursing and midwifery practice for everyone.</p>
</div>
<ul class="footer-primary">
<li>
<a href="https://www.nmc.org.uk/about-us/" target="_top">About us</a>
<img alt="" src="https://www.nmc.org.uk/static/img/refresh/chevron-white.svg"/>
</li>
<li>
<a href="https://www.nmc.org.uk/standards/" target="_top">Standards</a>
<img alt="" src="/static/img/refresh/chevron-white.svg"/>
</li>
<li>
<a href="https://www.nmc.org.uk/education/" target="_top">Education</a>
<img alt="" src="https://www.nmc.org.uk/static/img/refresh/chevron-white.svg"/>
</li>
<li>
<a href="https://www.nmc.org.uk/concerns-nurses-midwives/" target="_top">Concerns</a>
<img alt="" src="https://www.nmc.org.uk/static/img/refresh/chevron-white.svg"/>
</li>
<li>
<a href="https://www.nmc.org.uk/registration/" target="_top">Registration</a>
<img alt="" src="https://www.nmc.org.uk/static/img/refresh/chevron-white.svg"/>
</li>
<li>
<a href="https://nmc.pinpointhq.com/" target="_blank">Careers</a>
<img alt="" src="https://www.nmc.org.uk/static/img/refresh/chevron-white.svg"/>
</li>
</ul>
<p class="sr-only">Connect with us on social media using the links below:</p>
<ul class="footer-social">
<li>
<br/>
<a class="footer-social-media-item" href="https://www.facebook.com/nmcuk" target="_blank">
<span class="sr-only">Facebook</span>
<div>
<img alt="" class="" src="https://www.nmc.org.uk/globalassets/icons/social-media-icons/facebook-icon.png?h=40&amp;w=0&amp;scale=both"/>
</div>
</a>
</li>
<li>
<br/>
<a class="footer-social-media-item" href="https://twitter.com/nmcnews" target="_blank">
<span class="sr-only">X</span>
<div>
<img alt="" class="" src="https://www.nmc.org.uk/globalassets/icons/social-media-icons/x-icon.png?h=40&amp;w=0&amp;scale=both"/>
</div>
</a>
</li>
<li>
<br/>
<a class="footer-social-media-item" href="https://www.instagram.com/nmcnews/" target="_blank">
<span class="sr-only">Instagram</span>
<div>
<img alt="" class="" src="https://www.nmc.org.uk/globalassets/icons/social-media-icons/instagram-icon.png?h=40&amp;w=0&amp;scale=both"/>
</div>
</a>
</li>
<li>
<br/>
<a class="footer-social-media-item" href="https://www.linkedin.com/company/the-nursing-and-midwifery-council/mycompany/" target="_blank">
<span class="sr-only">LinkedIn</span>
<div>
<img alt="" class="" src="https://www.nmc.org.uk/globalassets/icons/social-media-icons/linkedin-icon.png?h=40&amp;w=0&amp;scale=both"/>
</div>
</a>
</li>
</ul>
<ul class="footer-secondary">
<li><a href="https://www.nmc.org.uk/accessibility/">Accessibility</a></li>
<li><a href="https://www.nmc.org.uk/terms-and-conditions/cookies/">Cookies</a></li>
<li><a href="https://www.nmc.org.uk/terms-and-conditions/">Terms &amp; conditions</a></li>
<li><a href="https://www.nmc.org.uk/modern-slavery-statement/">Modern slavery statement</a></li>
<li><a href="https://www.nmc.org.uk/link/95ff4fc0d18441d7be647761132c4b33.aspx">Environmental Sustainability Plan</a></li>
<li><a href="https://www.nmc.org.uk/contact-us/data-protection/publication-scheme/">Publication scheme</a></li>
<li><a href="https://www.nmc.org.uk/contact-us/data-protection/privacy-notice/">Privacy notice</a></li>
<li><a href="https://www.nmc.org.uk/contact-us/data-protection/">FOI and Data Protection requests</a></li>
<li><a href="https://www.nmc.org.uk/about-us/working-with-others/procurement/">Procurement</a></li>
<li><a href="https://www.nmc.org.uk/staff-area/">Staff area and webmail</a></li>
<li><a href="https://www.nmc.org.uk/tone-of-voice/">Our tone of voice guidelines</a></li>
</ul>
<div class="footer-small">
<p><small>© The Nursing and Midwifery Council 2025. The NMC is a registered charity in England and Wales (1091434) and Scotland (SC03836).</small></p>
</div>
</div>
</div>
</div>
</footer>
</body>
</html>
