<!doctype html>
<html lang="en-GB">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>NMC UK — Maintenance</title>
    <meta name="description" content="Nursing and Midwifery Council (NMC) UK maintenance notice." />
    <style>
      :root {
        --nmc-teal: #00749B;      /* primary teal */
        --nmc-purple: #4A5AD4;    /* main navigation purple/indigo */
        --nmc-navy: #0A2239;      /* deep footer navy */
        --nmc-pink: #D94C98;      /* action pink (login-style) */
        --page-bg: #ffffff;       /* requested white background */
        --text: #0f172a;          /* slate-900 */
        --muted: #516173;         /* slate-500 */
        --border: #e5e7eb;        /* light border */
      }

      * { box-sizing: border-box; }
      html, body { height: 100%; }
      body {
        margin: 0;
        background: var(--page-bg);
        color: var(--text);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Inter, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif;
        line-height: 1.5;
      }

      .container { max-width: 1120px; margin-inline: auto; padding-inline: 1rem; }

      /* Utility top bar */
      .utility { background: #fff; border-bottom: 1px solid var(--border); font-size: .875rem; }
      .utility .row { display: flex; gap: 1rem; align-items: center; justify-content: space-between; padding: .5rem 0; }
      .util-links, .util-actions { display: flex; gap: 1rem; flex-wrap: wrap; }
      .utility a { color: #374151; text-decoration: none; }
      .utility a:hover, .utility a:focus { text-decoration: underline; }

      /* Brand bar */
      .brandbar { background: #fff; border-bottom: 1px solid var(--border); }
      .brandbar .row { display: flex; align-items: center; justify-content: space-between; gap: 1rem; padding: .75rem 0; }
      .brand { display: flex; align-items: center; gap: .75rem; }
      .brand .logo { height: 46px; width: auto; display: block; }
      .brand .name { font-weight: 700; font-size: 1.0625rem; letter-spacing: .2px; white-space: nowrap; color: #111827; }

      /* Primary nav */
      .navbar { background: var(--nmc-purple); color: #fff; }
      .navbar nav { display: flex; gap: .25rem; overflow-x: auto; padding: .5rem 0; }
      .navbar a { color: #fff; text-decoration: none; font-weight: 600; padding: .5rem .75rem; border-radius: .5rem; }
      .navbar a:hover, .navbar a:focus { background: rgba(255,255,255,.12); }

      /* Main layout */
      main { padding: 1.5rem 0 2.5rem; }
      .grid { display: grid; grid-template-columns: 1fr 320px; gap: 2rem; align-items: start; }
      @media (max-width: 900px) { .grid { grid-template-columns: 1fr; } }

      /* Left column */
      .breadcrumb { font-size: .875rem; color: #6b7280; margin-bottom: .75rem; }
      .breadcrumb a { color: #374151; text-decoration: none; }
      .breadcrumb a:hover { text-decoration: underline; }

      h1 { margin: 0 0 .25rem; font-size: clamp(1.6rem, 1.1rem + 1.6vw, 2.25rem); font-weight: 800; letter-spacing: -.01em; }
      .lead { color: var(--muted); margin-bottom: 1rem; }
      .panel { background: #fff; border: 1px solid rgba(10,34,57,.10); border-radius: 12px; box-shadow: 0 8px 30px rgba(10,34,57,.06); overflow: hidden; }
      .panel .body { padding: 1.25rem; }
      .btn { display: inline-flex; align-items: center; gap: .5rem; padding: .6rem .9rem; font-weight: 700; border-radius: .5rem; text-decoration: none; }
      .btn-primary { background: var(--nmc-pink); color: #fff; }
      .btn-primary:hover { filter: brightness(.95); }

      .notice {
        background: #F0F7FA; border: 1px solid color-mix(in oklab, var(--nmc-teal) 35%, white);
        color: #0b3a4d; padding: 1rem; border-radius: .5rem; margin: 1rem 0 0;
      }

      /* Right column */
      .iwant { border: 1px solid var(--border); }
      .iwant .head { background: #e6ecff; color: #111827; padding: .9rem 1rem; border-bottom: 1px solid var(--border); font-weight: 800; }
      .iwant ul { list-style: none; padding: .5rem; margin: 0; display: grid; gap: .5rem; }
      .iwant a { display: flex; align-items: center; justify-content: space-between; text-decoration: none; padding: .9rem 1rem; border-radius: .5rem; background: #0b7aa61a; color: #0b4961; font-weight: 700; border: 1px solid #0b7aa62b; }
      .iwant a:hover { background: #0b7aa629; }

      /* Promo footer band */
      .promo { background: var(--nmc-purple); color: #fff; padding: 2rem 0; }
      .promo .cols { display: grid; grid-template-columns: 1.2fr 1fr 1fr 1fr; gap: 1.5rem; }
      .promo h2 { font-size: clamp(1.5rem, 1.1rem + 1.5vw, 2rem); margin: 0 0 .5rem; }
      .promo .big { font-size: clamp(2rem, 1.4rem + 2.2vw, 3rem); font-weight: 900; line-height: 1.1; opacity: .95; }
      .promo a { color: #fff; text-decoration: none; }
      .promo a:hover { text-decoration: underline; }
      .promo ul { list-style: none; padding: 0; margin: 0; display: grid; gap: .4rem; }
      @media (max-width: 900px) { .promo .cols { grid-template-columns: 1fr 1fr; } }
      @media (max-width: 560px) { .promo .cols { grid-template-columns: 1fr; } }

      /* Fine footer */
      .site-footer { background: var(--nmc-navy); color: #cbd5e1; }
      .site-footer .row { display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; gap: .75rem 1rem; padding: 1rem 0; }
      .foot-links, .legal { display: flex; flex-wrap: wrap; gap: .75rem 1rem; }
      .site-footer a { color: #e2e8f0; text-decoration: none; }
      .site-footer a:hover { text-decoration: underline; }
      .copy { opacity: .9; }

      /* Accessibility */
      .skip-link { position: absolute; left: -9999px; top: auto; width: 1px; height: 1px; overflow: hidden; }
      .skip-link:focus { left: 1rem; top: 1rem; width: auto; height: auto; padding: .5rem .75rem; background: #fff; color: #000; border-radius: .5rem; box-shadow: 0 2px 8px rgba(0,0,0,.15); }
    </style>
  </head>
  <body>
    <a href="#main" class="skip-link">Skip to main content</a>

    <!-- Utility top strip -->
    <div class="utility">
      <div class="container row">
        <div class="util-links">
          <a href="#">NMC Online</a>
          <a href="#">Contact us</a>
        </div>
        <div class="util-actions">
          <a href="#">Search the register</a>
          <a href="#">Employer confirmations</a>
        </div>
      </div>
    </div>

    <!-- Brand bar with logo/name -->
    <div class="brandbar">
      <div class="container row">
        <div class="brand">
          <!-- Placeholder wordmark; swap for official NMC logo when approved -->
          <svg class="logo" width="160" height="48" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg" aria-label="NMC logo placeholder">
            <rect rx="10" ry="10" width="200" height="60" fill="var(--nmc-teal)" />
            <text x="100" y="38" text-anchor="middle" font-family="Arial, Helvetica, sans-serif" font-size="32" font-weight="700" fill="#fff">NMC</text>
          </svg>
          <span class="name">Nursing &amp; Midwifery Council (NMC)</span>
        </div>
      </div>
    </div>

    <!-- Purple navigation bar -->
    <div class="navbar" role="navigation" aria-label="Primary">
      <div class="container">
        <nav>
          <a href="#">About us</a>
        
        </nav>
      </div>
    </div>

    <!-- Main -->
    <main id="main" class="container" role="main">
      <div class="breadcrumb">
        <strong>NMC Online</strong>
      </div>

      <div class="grid">
        <!-- Left column -->
        <section>
          <h1>NMC Online</h1>
          <p class="lead">Managing your registration online</p>

          <div class="panel">
            <div class="body">
 
              <div class="notice" role="status" aria-live="polite">
                <strong>Maintenance notice:</strong> We apologise for any inconvenience, we are performing maintenance to our portal. Please check back again later.
              </div>
            </div>
          </div>
        </section>

        <!-- Right column 
        <aside aria-label="I want to">
          <div class="panel iwant">
            <div class="head">I want to…</div>
            <ul>
              <li><a href="#">Find out about revalidation <span aria-hidden>›</span></a></li>
              <li><a href="#">Change my personal details <span aria-hidden>›</span></a></li>
              <li><a href="#">Add a qualification to my registration <span aria-hidden>›</span></a></li>
              <li><a href="#">Find out about tax relief <span aria-hidden>›</span></a></li>
              <li><a href="#">Look up registration codes <span aria-hidden>›</span></a></li>
            </ul>
          </div>
        </aside>-->
      </div>
    </main>

    <!-- Promo footer band -->
    <div class="promo">
      <div class="container cols">
        <div>
          <div class="big">Regulate<br/>Support<br/>Influence</div>
        </div>
        <div>
          <h2>We're the independent regulator</h2>
          <p>of more than 853,000 nurses, midwives and nursing associates. Our vision is safe, effective and kind nursing and midwifery practice for everyone.</p>
        </div>
        <div>
          <h2>Explore</h2>
          <ul>
            <li><a href="#">About us</a></li>
            <li><a href="#">Education</a></li>
            <li><a href="#">Registration</a></li>
          </ul>
        </div>
        <div>
          <h2>Guidance</h2>
          <ul>
            <li><a href="#">Standards</a></li>
            <li><a href="#">Concerns</a></li>
            <li><a href="#">Careers</a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Fine footer -->
    <footer class="site-footer" role="contentinfo">
      <div class="container row">
        <div class="foot-links">
          <a href="#">Accessibility</a>
          <a href="#">Cookies</a>
          <a href="#">Terms &amp; conditions</a>
          <a href="#">Privacy notice</a>
        </div>
        <div class="legal copy">© The Nursing and Midwifery Council 2025.</div>
      </div>
    </footer>
  </body>
</html>
