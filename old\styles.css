
:root{
  --nmc-primary: #00749B;
  --nmc-accent: #4A5AD4;
  --nmc-dark: #0A2239;
  --nmc-bg: #F5F7FA;
}

* { box-sizing: border-box; }
html { font-size: 16px; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Inter, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif;
  color: var(--nmc-dark);
  background: linear-gradient(180deg, white 0%, var(--nmc-bg) 100%);
}

.page {
  min-height: 100%;
  display: grid;
  place-items: center;
  padding: 2rem;
}

.card {
  width: 100%;
  max-width: 720px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 30px rgba(10,34,57,.08);
  overflow: hidden;
  border: 1px solid rgba(10,34,57,.06);
}

.header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 1.25rem 0.75rem;
  border-bottom: 1px solid rgba(10,34,57,.06);
}

.brand-lockup { display: flex; align-items: center; gap: .75rem; }
.brand-lockup img { height: 44px; width: auto; }
.brand-lockup .org { font-weight: 700; font-size: 1.125rem; letter-spacing: .2px; color: var(--nmc-dark); }
.brand-lockup .sub { font-size: .9rem; color: #516173; }

.content { padding: 2rem 1.5rem; }
h1 {
  margin: 0 0 .75rem;
  font-size: clamp(1.5rem, 1.2rem + 1.2vw, 2rem);
  color: var(--nmc-dark);
}

.message {
  font-size: 1.0625rem;
  line-height: 1.55;
  color: #334155;
}

.status { 
  margin-top: 1.25rem;
  display: inline-flex; 
  align-items: center; 
  gap: .5rem;
  padding: .5rem .75rem;
  border-radius: 999px;
  background: color-mix(in oklab, var(--nmc-primary) 10%, white);
  border: 1px solid color-mix(in oklab, var(--nmc-primary) 25%, white);
  font-weight: 600;
  color: var(--nmc-primary);
}

.footer {
  padding: 1rem 1.25rem 1.25rem;
  display: flex;
  flex-wrap: wrap;
  gap: .75rem 1rem;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(10,34,57,.06);
}

.links a {
  color: var(--nmc-accent);
  text-decoration: none;
  font-weight: 600;
}
.links a:focus, .links a:hover { text-decoration: underline; }

small.note { color: #516173; }

@media (prefers-color-scheme: dark) {
  body { background: #0b1320; color: #e6eaf1; }
  .card { background: #101827; border-color: #1e293b; }
  .header { border-color: #1e293b; }
  .footer { border-color: #1e293b; }
  .message { color: #c8d0db; }
  .brand-lockup .sub { color: #9aa8bc; }
  .status { 
    background: color-mix(in oklab, var(--nmc-primary) 20%, black);
    border-color: color-mix(in oklab, var(--nmc-primary) 40%, black);
    color: #bde9f7;
  }
}
